from pathlib import Path
import sys
from typing import Dict, <PERSON>, List, <PERSON><PERSON>


def local_execution_with_others_projects_modules(current_file: str, directory_to_go_back: int = 1) -> Path:
    """
    If in a local execution the current module uses resources from another \n
    module that is in the same project i.e.(this module is in the same \n
    directory as the other module) and they aren't in the same level. \n
    So you need to add the common dictory between the modules to the Paths of execution \n
    
    Args:
        `current_file` (str): file path who calls the other modules

    Returns:
        `Path`: common directory Path
    """
    
    src_folder = Path(current_file.split(":")[0].upper() + ":" + current_file.split(":")[1])
    for _ in range(directory_to_go_back): src_folder = src_folder.parent
    if src_folder.__str__() not in sys.path: sys.path.append(str(src_folder))
    if Path.cwd().__str__() not in sys.path: sys.path.append(Path.cwd().__str__())

    return src_folder

src_folder = local_execution_with_others_projects_modules(__file__, 3)

from Config.database import query_all_db, query_all_db_dw
import Config.vars as global_vars
from models.payment_method_data import *
from models.refund_log import EstornoLog, turn_estorno_log_to_dataclasse
from services.multi_payment_method_sale.multi_pm_vars import *
from utils import get_branch_id_in_acquirer
import services.lio_service as veloce
from models.card_refund_info import CardRefundInfo
from services.logger import create_logger

if __name__ == '__main__': 
    mpm_logger = create_logger('')
    mpm_logger.setLevel(10)
else: 
    mpm_logger = create_logger(__name__, without_handler = True)


def get_card_refund_info_tef(obj_estorno_log: dict) -> Tuple[Union[global_vars.ResultHandle, CardRefundInfo], EstornoLog]:

    refund_info = obj_estorno_log if isinstance(obj_estorno_log, EstornoLog) else turn_estorno_log_to_dataclasse(obj_estorno_log)

    payment_method_data, refund_info = match_refund_with_payment_method_tef(refund_info)

    if (isinstance(payment_method_data, global_vars.ResultHandle)):

        if payment_method_data.error_description != PAYMENT_METHODS_NOT_FOUND: return payment_method_data, refund_info

        mpm_logger.info("Buscando finalizador via SCOPE")
        scope_sale_info = sale_info_from_scope(refund_info.dataMovimento, 
                                               refund_info.codigoFilialOrigem, 
                                               refund_info.nsutef)
        
        if isinstance(scope_sale_info, global_vars.ResultHandle):
            mpm_logger.info("Não foi possivel obter os dados da venda via SCOPE")
            return payment_method_data, refund_info
            
        requesition_info = CardRefundInfo(nsu_tef=refund_info.nsutef, 
                                          nsu_external=scope_sale_info["nsu_host"],
                                          sale_date=refund_info.dataMovimento,
                                          payment_method_value=scope_sale_info["valor"],
                                          refund_value=refund_info.valorEstorno,
                                          installments=refund_info.parcelas,
                                          acquirer_code=scope_sale_info["cod_rede"],
                                          auth_code=refund_info.codigoAutorizacao)
        
    
    else:

        requesition_info = CardRefundInfo(nsu_tef=refund_info.nsutef, 
                                          nsu_external=payment_method_data.external_nsu,
                                          sale_date=refund_info.dataMovimento,
                                          payment_method_value=payment_method_data.payment_method_value,
                                          refund_value=refund_info.valorEstorno,
                                          installments=refund_info.parcelas,
                                          acquirer_code=payment_method_data.acquirer_code,
                                          auth_code=refund_info.codigoAutorizacao)

    
    branch_in_acquirer, refund_info = get_branch_id_in_acquirer(refund_info, requesition_info.acquirer_code)
    if isinstance(branch_in_acquirer, global_vars.ResultHandle):
        return branch_in_acquirer, refund_info
    
    requesition_info.terminal = branch_in_acquirer.terminal
    requesition_info.establishment_code = branch_in_acquirer.establishment
    
    return requesition_info, refund_info
    


def get_card_refund_info_pos(obj_estorno_log: dict, pos_info: dict) -> Tuple[Union[global_vars.ResultHandle, CardRefundInfo], EstornoLog]:

    refund_info = obj_estorno_log if isinstance(obj_estorno_log, EstornoLog) else turn_estorno_log_to_dataclasse(obj_estorno_log)

    if pos_info['adquirente'].lower() == global_vars.CIELO_ACQUIRE_NAME.lower():

        card_requesition_info, refund_info = deal_with_cielo_pos_refund_info(refund_info, pos_info)

        if isinstance(card_requesition_info, global_vars.ResultHandle):
            return card_requesition_info, refund_info
        
        return card_requesition_info, refund_info
        
        
    elif pos_info['adquirente'].lower() == global_vars.GETNET_ACQUIRE_NAME.lower():

        card_requesition_info, refund_info = deal_with_getnet_pos_refund_info(refund_info, pos_info)

        if isinstance(card_requesition_info, global_vars.ResultHandle):
            return card_requesition_info, refund_info
        
        return card_requesition_info, refund_info
        
    else:

        msg = f"Adquirente {pos_info['adquirente']} não foi identificado"
        mpm_logger.error(msg)
        refund_info.status = global_vars.STATUS_PENDENCY_FOR_CSC
        refund_info.motivo = msg
        return global_vars.ResultHandle.Fail(f"Adquirente {pos_info['adquirente']} não foi identificado"), refund_info

        
        

def deal_with_cielo_pos_refund_info(refund_info: EstornoLog, pos_info) -> Union[global_vars.ResultHandle, EstornoLog]:

    try:
        if (pos_info["numero_ec"] is None):

            branch_in_acquirer, refund_info = get_branch_id_in_acquirer(refund_info, 102)
            if isinstance(branch_in_acquirer, global_vars.ResultHandle):
                return branch_in_acquirer, refund_info

            ec_cielo_pos = branch_in_acquirer.establishment

        else:

            if (len(pos_info["numero_ec"]) == 16):
                ec_cielo_pos: str = pos_info["numero_ec"][:-4].lstrip("0")

            
        requesition_info = CardRefundInfo(nsu_tef=refund_info.nsuhost, 
                                            nsu_external=refund_info.nsuhost,
                                            sale_date=refund_info.dataMovimento,
                                            payment_method_value=pos_info["valor"],
                                            refund_value=refund_info.valorEstorno,
                                            installments=refund_info.parcelas,
                                            acquirer_code=global_vars.CIELO_ACQUIRE_SCOPE_CODE,
                                            auth_code=pos_info["auth_code"],
                                            establishment_code=ec_cielo_pos)
        
        refund_info.codigoAutorizacao = pos_info["auth_code"]
        return requesition_info, refund_info

    except Exception as e:
        msg = f"Erro ao consolidar as informações de estorno de POS {global_vars.CIELO_ACQUIRE_NAME}"
        refund_info.status = global_vars.STATUS_PENDENCY_FOR_CSC
        refund_info.motivo = msg
        mpm_logger.error(msg)
        mpm_logger.error(f"Detalhes do erro: {e}")
        return global_vars.ResultHandle.Fail(msg), refund_info


def deal_with_getnet_pos_refund_info(refund_info: EstornoLog, pos_info) -> Union[global_vars.ResultHandle, EstornoLog]:

    try:
        auth_code = veloce.extract_getnet_auth_code_in_pos(pos_info["json_retorno"])
        ec_getnet_pos = veloce.extract_ecnumber_getnet_pos(pos_info["json_retorno"])
        external_nsu = veloce.extract_getnet_external_nsu_in_pos(pos_info["json_retorno"])
        terminal = veloce.extract_getnet_terminal_in_pos(pos_info["json_retorno"])
        
        card_requesition_info = CardRefundInfo(nsu_tef=external_nsu,
                                            nsu_external = external_nsu,
                                            establishment_code=ec_getnet_pos,
                                            acquirer_code=global_vars.GETNET_ACQUIRE_SCOPE_CODE,
                                            installments=refund_info.parcelas,
                                            auth_code=auth_code,
                                            payment_method_value=pos_info["valor"],
                                            refund_value=refund_info.valorEstorno,
                                            sale_date=refund_info.dataMovimento,
                                            terminal=terminal)
        

        refund_info.codigoAutorizacao = auth_code
        refund_info.nsutef = external_nsu
        refund_info.nsuhost = external_nsu
        return card_requesition_info, refund_info
    
    except Exception as e:
        msg = f"Erro ao consolidar as informações de estorno de POS {global_vars.GETNET_ACQUIRE_NAME}"
        refund_info.status = global_vars.STATUS_PENDENCY_FOR_CSC
        refund_info.motivo = msg
        mpm_logger.error(msg)
        mpm_logger.error(f"Detalhes do erro: {e}")
        return global_vars.ResultHandle.Fail(msg), refund_info

def match_refund_with_payment_method_tef(obj_estorno_log: Union[EstornoLog, Dict]) -> Tuple[Union[global_vars.ResultHandle, PaymentMethodData], EstornoLog]:

    refund_info = obj_estorno_log if isinstance(obj_estorno_log, EstornoLog) else turn_estorno_log_to_dataclasse(obj_estorno_log)

    payment_methods_data = get_sale_payment_methods(refund_info.idEstorno)
    if isinstance(payment_methods_data, global_vars.ResultHandle): 
        return deal_with_erro(refund_info, payment_methods_data.error_description)
        

    if len(payment_methods_data) == 1: return payment_methods_data[0], refund_info
        
    
    mpm_logger.info("Estorno faz parte de uma comnpra com mais de 1 finalziador")
    
    payment_method_of_refund = list(
                filter(
                    lambda method_data: method_data.nsu_tef == int(refund_info.nsutef),
                    payment_methods_data
                ))
    
    if len(payment_method_of_refund) == 0: 
        mpm_logger.warning(NO_MATCH_BETWEEN_REFUND_AND_PAYMENT_METHOD)
        return deal_with_erro(refund_info, NO_MATCH_BETWEEN_REFUND_AND_PAYMENT_METHOD)
        
    
    if len(payment_method_of_refund) > 1:
        mpm_logger.warning("Repetição de finalizadores (NSUTEF iguais dentro de um cupom)")
        mpm_logger.warning("será usado o primeiro finalizador")
        
    
    return payment_method_of_refund[0], refund_info


def get_sale_payment_methods(id_estorno) -> Union[global_vars.ResultHandle, List[PaymentMethodData]]:

    query_payment_method_of_sale = """
                    SELECT
                    ev.ID_ESTORNO,
                    ev.FILIAL_ORIGEM,
                    ev.VALOR_CUPOM,
                    ev.VALOR_ESTORNO,
                    fl_vda.FLVD_VL_FLZ AS VALOR_FINALIZADOR,
                    fl_vda.FLVD_NR_NSU_TEF,
                    fl_vda.REDE_SCOPE,
                    ev.NUMERO_COO,
                    fl_vda.NSU_AUTORIZADORA_TEF,
                    fl_vda.FLVD_NR_ECF_MOV
                    FROM CosmosRPA.dbo.ESTORNO_VENDA ev (nolock)
                    INNER JOIN CosmosFL.dbo.FLZ_VDA fl_vda (nolock)
                        ON (ev.FILIAL_ORIGEM = fl_vda.FLVD_CD_FILIAL_MOV 
                            and ev.DATA_MOVIMENTO = fl_vda.FLVD_DT_MOV
                            and ev.NUMERO_COO = fl_vda.FLVD_CT_VDA
                            )
                    WHERE 
                    ev.ID_ESTORNO = {id_estorno}
                    ORDER BY ev.DATA_DEVOLUCAO DESC
                    """

    query = query_payment_method_of_sale.format(id_estorno = id_estorno)

    payment_info = query_all_db(query)

    if len(payment_info) == 0:
        msg = PAYMENT_METHODS_NOT_FOUND
        mpm_logger.warning(msg)
        return global_vars.ResultHandle.Fail(msg)
    try:
        result = list(map(load_payment_method_data_orm, payment_info))
    except Exception as e:
        return global_vars.ResultHandle.Fail(f"Sem dados de finalizador | {e}")
    
    return result


def sale_info_from_scope(data_venda:str, filial:int, nsu:str) -> Union[Dict, global_vars.ResultHandle]: 

    find_sale = """SELECT  *
                    FROM cosmosdw.scopehist.dbo.mensagem MSG (NOLOCK) 
                    WHERE
                    convert(int, MSG.cod_filial) = convert(int, '{branch}')
                    AND RIGHT(isnull(MSG.NSU,'0'), 6) = convert(int, '{nsu_tef}')
                    AND convert(date, MSG.dthr_server) = CONVERT(DATE, '{sale_date}')
                    """

    find_sale = find_sale.format(branch=filial,
                                sale_date = data_venda,
                                nsu_tef = nsu)

    try:
        sale_info = query_all_db_dw(find_sale)
    except:
        mpm_logger.error("Erro ao obter dados da venda na VDA")
        return global_vars.ResultHandle.Fail(ERROR_SCOPE_QUERY)

    if len(sale_info) < 1 or sale_info is None:
        mpm_logger.error("Não há informação auxiliares sobre essa venda")
        return global_vars.ResultHandle.Fail(ERROR_SCOPE_QUERY)
    
    if len(sale_info) > 1:
        mpm_logger.warning("Mais de um registro de venda foi encontrado na scope")

    return sale_info[0]



def deal_with_erro(refund_info: EstornoLog, msg: str = None) -> Tuple[global_vars.ResultHandle, EstornoLog]:

    mpm_logger.warning(msg)
    refund_info.motivo = msg[:254]
    refund_info.status = global_vars.STATUS_PENDENCY_FOR_CSC

    return global_vars.ResultHandle.Fail(msg), refund_info



if __name__ == "__main__":

    obj_estorno_log = {
        "idEstorno": 285852,
        "nsutef":103535
    }

    match_refund_with_payment_method_tef(obj_estorno_log)

    print("fim")
