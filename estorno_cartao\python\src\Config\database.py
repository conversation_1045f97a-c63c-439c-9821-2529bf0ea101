
import pyodbc
from typing import <PERSON>ple
from pyodbc import <PERSON><PERSON><PERSON>, Connection


def get_db_cursor()-> <PERSON><PERSON>[Cursor, Connection]:
  
  '''PRODUÇÃO'''
  server = '10.4.100.5' 
  database = 'cosmosrpa' 
  username = 'usercosmos' 
  password = 'KOS1520MO'
  
  '''HOMOLOGAÇÃO'''
  # server = 'andromeda2' 
  # database = 'cosmosrpa' 
  # username = 'syscosmos' 
  # password = 'COSMOS'
  
  connection = pyodbc.connect('DRIVER={SQL Server};SERVER='+server+';DATABASE='+database+';UID='+username+';PWD='+ password + ";ConnectTimeout={180}" )
  db_cursor = connection.cursor()
  
  return db_cursor, connection



def query_all_db(sql, *params):
  cursor , connection = get_db_cursor()
  cursor.execute(sql, params)
  columns = [column[0] for column in cursor.description]
  results = []
  for row in cursor.fetchall():
    results.append(dict(zip(columns, row)))
  
  cursor.close()
  connection.close()
  
  
  return results

def query_one_db(sql, *params):
  cursor , connection = get_db_cursor()
  cursor.execute(sql, params)
  row = cursor.fetchone()
  if row != None:
    columns = [column[0] for column in cursor.description]

    cursor.close()
    connection.close()
    return dict(zip(columns, row))
  
  else:
    
    cursor.close()
    connection.close()
    return None

def update_db(sql, *params):
  cursor , connection = get_db_cursor()
  cursor.execute(sql, params)
  cursor.commit()
  cursor.close()
  connection.close()
  
  
def insert_db(sql, *params):
  cursor , connection = get_db_cursor()
  cursor.execute(sql, params)
  cursor.commit()
  cursor.close()
  connection.close()
  


def deletar(query , *args):
        """

        ## Consulta usada para deletar itens
        Caso a query necessite de valores, eles podem ser passados 
        simplismente adicionando-se `argumentos` dentro da função

        """
        tupla = tuple(args)
        cursor , connection = get_db_cursor()

        try:
            
            cursor.execute(query, tupla)
            saida = cursor.commit()
            cursor.close()
            connection.close()
            
        except Exception as erro:
                
            print(f'Erro de consulta: {erro}')

            cursor.close()
            connection.close()
            return erro
        
        cursor.close()
        connection.close()
        return saida




def get_db_dw_cursor()-> Tuple[Cursor, Connection]:
  
  '''PRODUÇÃO'''
  server = '10.4.100.6' 
  database = 'scopehist' 
  username = 'usercosmos' 
  password = 'KOS1520MO'
  
  '''HOMOLOGAÇÃO'''
  # server = 'andromeda2' 
  # database = 'cosmosrpa' 
  # username = 'syscosmos' 
  # password = 'COSMOS'
  
  connection = pyodbc.connect('DRIVER={SQL Server};SERVER='+server+';DATABASE='+database+';UID='+username+';PWD='+ password + ";ConnectTimeout={180}" )
  db_cursor = connection.cursor()
  
  return db_cursor, connection



def query_all_db_dw(sql, *params):

  #sql = "select top 1 * FROM cosmosdw.scopehist.dbo.mensagem MSG (NOLOCK)"
  cursor , connection = get_db_dw_cursor()
  cursor.execute(sql, params)
  columns = [column[0] for column in cursor.description]
  results = []
  for row in cursor.fetchall():
    results.append(dict(zip(columns, row)))
  
  cursor.close()
  connection.close()

  return results