from pathlib import Path
import sys


def local_execution_with_others_projects_modules(current_file: str, directory_to_go_back: int = 1) -> Path:
    """
    If in a local execution the current module uses resources from another \n
    module that is in the same project i.e.(this module is in the same \n
    directory as the other module) and they aren't in the same level. \n
    So you need to add the common dictory between the modules to the Paths of execution \n
    
    Args:
        `current_file` (str): file path who calls the other modules

    Returns:
        `Path`: common directory Path
    """
    
    src_folder = Path(current_file.split(":")[0].upper() + ":" + current_file.split(":")[1])
    for _ in range(directory_to_go_back): src_folder = src_folder.parent
    if src_folder.__str__() not in sys.path: sys.path.append(str(src_folder))
    if Path.cwd().__str__() not in sys.path: sys.path.append(Path.cwd().__str__())

    return src_folder

if __name__ == "__main__":
    src_folder = local_execution_with_others_projects_modules(__file__, 3)


from services.logger import create_logger
from Connections.search_db import SearchDb
from services.estorno_service import record_processment_in_bd
from models.refund_log import estorno_venda_orm
    


if __name__ == '__main__': 
    manual_treat_logger = create_logger('')
    manual_treat_logger.setLevel(10)
else: 
    manual_treat_logger = create_logger(__name__, without_handler = True)


researcher = SearchDb()

refund_request_info_raw = researcher.search_refund_by_refund_id_in_estorno_venda(309009)
refund_request_info = estorno_venda_orm(refund_request_info_raw[0][0])

refund_request_info.status = "FINALIZADO"
refund_request_info.motivo = "Estorno resolvido manualmente"
record_processment_in_bd(refund_request_info)

print("fim")


