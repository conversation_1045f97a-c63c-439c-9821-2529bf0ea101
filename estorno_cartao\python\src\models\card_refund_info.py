from pydantic import BaseModel, model_validator, Field
from typing import Optional

class CardRefundInfo(BaseModel):
    nsu_tef: str
    nsu_external: str
    payment_method_value: float
    refund_value: float
    installments: int
    acquirer_code: int
    auth_code: str
    establishment_code: Optional[str] = None
    terminal: Optional[str] = None
    sale_date: Optional[str] = None
    

    @model_validator(mode='after')
    def format_fields(self):

        self.payment_method_value = float(self.payment_method_value)
        self.refund_value = float(self.refund_value)
        self.installments = int(self.installments)
        self.acquirer_code = int(self.acquirer_code)

        return self
        
        





