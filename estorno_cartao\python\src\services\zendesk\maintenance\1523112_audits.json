{"audits": [{"id": 35676540595597, "ticket_id": 1523112, "created_at": "2025-04-08T13:10:00Z", "author_id": 30108526245517, "metadata": {"system": {"client": "python-requests/2.27.1", "ip_address": "**************", "location": "<PERSON><PERSON>, MG, Brazil", "latitude": -21.749, "longitude": -43.4382}, "custom": {}}, "events": [{"id": 35676540595725, "type": "Comment", "author_id": 27514623600781, "body": "Houve um problema no atendimento automatizado de estorno. Ele foi direcionado para o setor de CSC\n\nDetalhes do estorno:\n\nVenda do tipo Pix Mercad com finalizador None\n\n**Motivo:** Divergência de valores entre troca-dev e outras fontes\n\n**Nome do cliente:** LEONARDO HENRIQUE\n\n**Filial da venda:** 7321; **CNPJ filial da venda:** 004.899.316/0321-50; **Filial do pedido de estorno:** 7321;\n\n**NSU TEF:** 0; **NSU HOST:** 0; **TID:** None;\n\n**Nº cupom:** 61414; **COO:** 79895; **Número pedido E-commerce:** 152249025367101;\n\n**Telefone:** 81992360634; **e-mail:** <EMAIL>; **cartão sempre:** 83614020\n\n**Nº troca-devolução:** 2737280; **ID estorno:** 390299; **Nº pré-venda:** 308038889;\n\n**Código de autorização:** None; **Dígitos do cartão:** None;", "html_body": "<div class=\"zd-comment\" dir=\"auto\"><p dir=\"auto\">Houve um problema no atendimento automatizado de estorno. Ele foi direcionado para o setor de CSC</p>\n    <p dir=\"auto\">Detalhes do estorno:</p>\n    <p dir=\"auto\">Venda do tipo Pix Mercad com finalizador None</p>\n\n    <p dir=\"auto\"><strong>Motivo: </strong>Divergência de valores entre troca-dev e outras fontes</p>\n\n    <p dir=\"auto\"><strong>Nome do cliente: </strong>LEONARDO HENRIQUE</p>\n\n    <p dir=\"auto\">\n        <strong>Filial da venda: </strong> 7321;\n        <strong>CNPJ filial da venda: </strong> 004.899.316/0321-50;\n        <strong>Filial do pedido de estorno: </strong> 7321; \n    </p>\n\n    <p dir=\"auto\">\n        <strong>NSU TEF: </strong> 0; \n        <strong>NSU HOST: </strong> 0;\n        <strong>TID: </strong> None;\n    </p>\n\n    <p dir=\"auto\">\n        <strong>Nº cupom: </strong> 61414; \n        <strong>COO: </strong> 79895; \n        <strong>Número pedido E-commerce: </strong> 152249025367101; \n    </p>\n\n    <p dir=\"auto\">\n        <strong>Telefone: </strong> 81992360634; \n        <strong>e-mail: </strong> <EMAIL>; \n        <strong>cartão sempre: </strong> 83614020\n    </p>\n\n    <p dir=\"auto\">\n        <strong>Nº troca-devolução: </strong> 2737280; \n        <strong>ID estorno: </strong> 390299;\n        <strong>Nº pré-venda: </strong> 308038889;\n    </p>\n\n    <p dir=\"auto\">\n        <strong>Código de autorização: </strong> None; \n        <strong>Dígitos do cartão: </strong> None;\n    </p></div>", "plain_body": "Houve um problema no atendimento automatizado de estorno. Ele foi direcionado para o setor de CSC \n     Detalhes do estorno: \n     Venda do tipo Pix Mercad com finalizador None \n\n     Motivo: Divergência de valores entre troca-dev e outras fontes \n\n     Nome do cliente: LEONARDO HENRIQUE \n\n     \n        Filial da venda:  7321;\n        CNPJ filial da venda:  004.899.316/0321-50;\n        Filial do pedido de estorno:  7321; \n     \n\n     \n        NSU TEF:  0; \n        NSU HOST:  0;\n        TID:  None;\n     \n\n     \n        Nº cupom:  61414; \n        COO:  79895; \n        Número pedido E-commerce:  152249025367101; \n     \n\n     \n        Telefone:  81992360634; \n        e-mail:  <EMAIL>; \n        cartão sempre:  83614020\n     \n\n     \n        Nº troca-devolução:  2737280; \n        ID estorno:  390299;\n        Nº pré-venda:  308038889;\n     \n\n     \n        Código de autorização:  None; \n        Dígitos do cartão:  None;", "public": true, "attachments": [], "audit_id": 35676540595597}, {"id": 35676540595853, "type": "Create", "value": "27354928086413", "field_name": "brand_id"}, {"id": 35676540595981, "type": "Create", "value": "26546858689677", "field_name": "group_id"}, {"id": 35676540596109, "type": "Create", "value": "27514623600781", "field_name": "requester_id"}, {"id": 35676540596237, "type": "Create", "value": "RPA - Estorno - valor 43.89 - LEONARDO HENRIQUE", "field_name": "subject"}, {"id": 35676540596365, "type": "Create", "value": "26546968004109", "field_name": "ticket_form_id"}, {"id": 35676540596493, "type": "Create", "value": ["7321_rec52_conbovis_pe_ef", "atualizador_agente", "cancelamento_cancelamento_venda_em_pix", "lojas_extrafarma", "ptp_otc_cancelamento", "tipo_venda_pix_ecommerce", "venda_finalizada_sim"], "field_name": "tags"}, {"id": 35676540596621, "type": "Create", "value": "tipo_venda_pix_ecommerce", "field_name": "26503899038349"}, {"id": 35676540596749, "type": "Create", "value": "2025-04-04", "field_name": "26503899190157"}, {"id": 35676540596877, "type": "Create", "value": "LEONARDO HENRIQUE", "field_name": "26503906400909"}, {"id": 35676540597005, "type": "Create", "value": "152249025367101", "field_name": "26503917803917"}, {"id": 35676540597133, "type": "Create", "value": "lojas_extrafarma", "field_name": "26504306260749"}, {"id": 35676540597261, "type": "Create", "value": "cancelamento_cancelamento_venda_em_pix", "field_name": "26503910871949"}, {"id": 35676540597389, "type": "Create", "value": "0", "field_name": "27486246039309"}, {"id": 35676540597517, "type": "Create", "value": "43,89", "field_name": "27564388165645"}, {"id": 35676540597645, "type": "Create", "value": "7321_rec52_conbovis_pe_ef", "field_name": "27259375759757"}, {"id": 35676540597773, "type": "Create", "value": "43,89", "field_name": "27564327477005"}, {"id": 35676540597901, "type": "Create", "value": "Divergência de valores entre troca-dev e outras fontes", "field_name": "26503905836941"}, {"id": 35676540598029, "type": "Create", "value": "2737280", "field_name": "33697924658573"}, {"id": 35676540598157, "type": "Create", "value": "venda_finalizada_sim", "field_name": "26503889163021"}, {"id": 35676540598285, "type": "Create", "value": "0", "field_name": "26503914135309"}, {"id": 35676540598413, "type": "Create", "value": "0", "field_name": "26503888721421"}, {"id": 35676540598541, "type": "Create", "value": "0", "field_name": "28062908323597"}, {"id": 35676540598669, "type": "Create", "value": "0", "field_name": "26503938841741"}, {"id": 35676540598797, "type": "Create", "value": "0", "field_name": "26503910689165"}, {"id": 35676540598925, "type": "Create", "value": "0", "field_name": "26503944419853"}, {"id": 35676540599053, "type": "Create", "value": "0", "field_name": "26503955275021"}, {"id": 35676540599181, "type": "Create", "value": "0", "field_name": "26503916389773"}, {"id": 35676540599309, "type": "Create", "value": "0", "field_name": "26503936000013"}, {"id": 35676540599437, "type": "Create", "value": "0", "field_name": "26503916779021"}, {"id": 35676540599565, "type": "Create", "value": "0", "field_name": "26503967729549"}, {"id": 35676540599693, "type": "Create", "value": "0", "field_name": "28301149944973"}, {"id": 35676540599821, "type": "Create", "value": "0", "field_name": "28302727916685"}, {"id": 35676540599949, "type": "Create", "value": "0", "field_name": "26504533547917"}, {"id": 35676540600077, "type": "Create", "value": "0", "field_name": "28302888560525"}, {"id": 35676540600205, "type": "Create", "value": "0", "field_name": "28062899418253"}, {"id": 35676540600333, "type": "Create", "value": "ptp_otc_cancelamento", "field_name": "26921919394957"}, {"id": 35676540600461, "type": "Create", "value": "0", "field_name": "28062955735437"}, {"id": 35676540600589, "type": "Create", "value": "0", "field_name": "27742843895309"}, {"id": 35676540600717, "type": "Create", "value": "0", "field_name": "26972777392525"}, {"id": 35676540600845, "type": "Create", "value": "0", "field_name": "27305252599949"}, {"id": 35676540600973, "type": "Create", "value": "atualizador_agente", "field_name": "27442246071693"}, {"id": 35676540601101, "type": "Create", "value": null, "field_name": "priority"}, {"id": 35676540601229, "type": "Create", "value": null, "field_name": "type"}, {"id": 35676540601357, "type": "Create", "value": "new", "field_name": "status"}, {"id": 35676540601485, "type": "Create", "value": "18758522787725", "field_name": "custom_status_id"}, {"id": 35676540601613, "type": "Change", "via": {"channel": "rule", "source": {"from": {"deleted": false, "title": "Set Tickets - Prioridade Normal", "id": 26943116808717, "revision_id": 8}, "rel": "trigger"}}, "value": "normal", "field_name": "priority", "previous_value": null}, {"id": 35676540601741, "type": "ScheduleAssignment", "via": {"channel": "rule", "source": {"from": {"deleted": false, "title": "Business Hour - CSC", "id": 28470985032077, "revision_id": 2}, "rel": "trigger"}}, "previous_schedule_id": null, "new_schedule_id": "26545046365581"}, {"id": 35676540601869, "type": "Notification", "via": {"channel": "rule", "source": {"from": {"deleted": false, "title": "Notificação abertura de ticket - Geral", "id": 28060921154317, "revision_id": 12}, "rel": "trigger"}}, "subject": "[Solicitação recebida] - Assunto - {{ticket.title}}  - {{ticket.id}}", "body": "Sua solicitação foi aberta com o ticket {{ticket.id}}, \npara acompanhar o andamento, entre na aba \"Meus chamados\",<a href=https://colaborador.zendesk.com/hc/pt-br/requests> clique aqui!</a>.\n\nAtenciosamente\n\n", "recipients": [27514623600781]}, {"id": 35676540601997, "type": "Change", "via": {"channel": "rule", "source": {"from": {"deleted": false, "title": "Formulario Campos Padrões Obrigatorios", "id": 34165046325517, "revision_id": 3}, "rel": "trigger"}}, "value": ["7321_rec52_conbovis_pe_ef", "atualizador_agente", "cancelamento_cancelamento_venda_em_pix", "form_default_fields_yes", "lojas_extrafarma", "ptp_otc_cancelamento", "tipo_venda_pix_ecommerce", "venda_finalizada_sim"], "field_name": "tags", "previous_value": ["7321_rec52_conbovis_pe_ef", "atualizador_agente", "cancelamento_cancelamento_venda_em_pix", "lojas_extrafarma", "ptp_otc_cancelamento", "tipo_venda_pix_ecommerce", "venda_finalizada_sim"]}, {"id": 35676540602125, "type": "Change", "via": {"channel": "rule", "source": {"from": {"deleted": false, "title": "Formulario Campos Padrões Obrigatorios", "id": 34165046325517, "revision_id": 3}, "rel": "trigger"}}, "value": "form_default_fields_yes", "field_name": "34113031835149", "previous_value": null}, {"id": 35676540615565, "type": "Create", "value": "CSC - 11 Horas", "field_name": "sla_policy"}], "via": {"channel": "api", "source": {"from": {}, "to": {"address": "<EMAIL>", "name": "ef7321"}, "rel": null}}}, {"id": 35676535805325, "ticket_id": 1523112, "created_at": "2025-04-08T13:10:01Z", "author_id": -1, "metadata": {"system": {}, "custom": {}, "parent_audit_id": "35676540595597"}, "events": [{"id": 35676552347405, "type": "Change", "previous_value": null, "value": {"minutes": 660, "seconds": 39600, "in_business_hours": true}, "field_name": "agent_work_time", "via": {"source": {"rel": "sla_target_change"}, "current_sla_policy": "CSC - 11 Horas"}}], "via": {"channel": "sla", "source": {"from": {}, "to": {}, "rel": null}}}, {"id": 35677439306765, "ticket_id": 1523112, "created_at": "2025-04-08T13:31:43Z", "author_id": 29027579313933, "metadata": {"system": {"client": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "ip_address": "**************", "location": "São Paulo, SP, Brazil", "latitude": -23.5475, "longitude": -46.6361}, "custom": {}}, "events": [{"id": 35677439306893, "type": "Change", "via": {"channel": "system", "source": {"to": {}, "from": {"id": 18758563852173, "title": "Atribuir tickets na resolução"}, "rel": "admin_setting"}}, "value": "29027579313933", "field_name": "assignee_id", "previous_value": null}, {"id": 35677439307021, "type": "Comment", "author_id": 29027579313933, "body": "Prezado(a) **ef7321**,\nSegue carta de cancelamento.\nApós o processo de cancelamento em nosso sistema, o reembolso para o cliente depende dos prazos estabelecidos pela administradora do cartão de crédito/débito. Nos casos de PIX, o reembolso é imediato.\n\nSugerimos orientar o cliente a entrar em contato com o emissor de seu cartão (o número fica no verso do cartão) para questionamentos sobre o prazo de devolução.\n\nEm caso de dúvidas, permanecemos a disposição nos seguintes canais:\nWhatsapp: (85) 99761-6870\nTelefone: (85) 3255-5491 / (85) 3255-5493\nEmail: <EMAIL>\n\nAtenciosamente,\n- CSC", "html_body": "<div class=\"zd-comment\" dir=\"auto\"><span style=\"background-color: transparent; color: #000000;\">Prezado(a)</span><span style=\"background-color: transparent; color: rgb(0,0,0);\"><strong> ef7321</strong></span>,&nbsp;<br>Segue carta de cancelamento.&nbsp;<br>Após o processo de cancelamento em nosso sistema, o reembolso para o cliente depende dos prazos estabelecidos pela administradora do cartão de crédito/débito. Nos casos de PIX, o reembolso é imediato.<br><br>Sugerimos orientar o cliente a entrar em contato com o emissor de seu cartão (o número fica no verso do cartão) para questionamentos sobre o prazo de devolução.<br>&nbsp;<br>Em caso de dúvidas, permanecemos a disposição nos seguintes canais:<br>Whatsapp: (85) 99761-6870&nbsp;<br>Telefone: (85) 3255-5491 / (85) 3255-5493<br>Email: <a href=\"mailto:<EMAIL>\" rel=\"noreferrer\"><EMAIL></a><br><br>Atenciosamente,<br>- CSC<br>&nbsp;<br></div>", "plain_body": "Prezado(a) ef7321,&nbsp;\nSegue carta de cancelamento.&nbsp;\nApós o processo de cancelamento em nosso sistema, o reembolso para o cliente depende dos prazos estabelecidos pela administradora do cartão de crédito/débito. Nos casos de PIX, o reembolso é imediato.\n\nSugerimos orientar o cliente a entrar em contato com o emissor de seu cartão (o número fica no verso do cartão) para questionamentos sobre o prazo de devolução.\n&nbsp;\nEm caso de dúvidas, permanecemos a disposição nos seguintes canais:\nWhatsapp: (85) 99761-6870&nbsp;\nTelefone: (85) 3255-5491 / (85) 3255-5493\nEmail: <EMAIL>\n\nAtenciosamente,\n- CSC\n&nbsp;", "public": true, "attachments": [{"url": "https://paguemenossac.zendesk.com/api/v2/attachments/35677392792461.json", "id": 35677392792461, "file_name": "43,89.pdf", "content_url": "https://paguemenossac.zendesk.com/attachments/token/0PsseM6CRXdekBwzfy72izB3J/?name=43%2C89.pdf", "mapped_content_url": "https://paguemenossac.zendesk.com/attachments/token/0PsseM6CRXdekBwzfy72izB3J/?name=43%2C89.pdf", "content_type": "application/pdf", "size": 96838, "width": null, "height": null, "inline": false, "deleted": false, "malware_access_override": false, "malware_scan_result": "malware_not_found", "thumbnails": []}], "audit_id": 35677439306765}, {"id": 35677439307149, "type": "Change", "value": "solved", "field_name": "status", "previous_value": "new"}, {"id": 35677439307277, "type": "Change", "value": "18758520612365", "field_name": "custom_status_id", "previous_value": "18758522787725"}, {"id": 35677439307405, "type": "Change", "value": "147", "field_name": "31723397869581", "previous_value": null}, {"id": 35677439307533, "type": "Change", "value": "147", "field_name": "31723406578573", "previous_value": null}, {"id": 35677439307661, "type": "Notification", "via": {"channel": "rule", "source": {"from": {"deleted": false, "title": "Notificar o solicitante sobre a Resolução do Ticket", "id": 18758520633357, "revision_id": 23}, "rel": "trigger"}}, "subject": "[Atualização] Ticket {{ticket.id}}", "body": "{{ticket.latest_comment_html}}.", "recipients": [27514623600781]}, {"id": 35677439316877, "type": "AgentMacroReference", "via": {"channel": "web", "source": {"from": {}, "to": {}, "rel": null}}, "macro_title": "Cancelamento::Resolvido Com Carta Anexada", "macro_id": "33616814329869", "macro_deleted": false}], "via": {"channel": "web", "source": {"from": {}, "to": {"address": "<EMAIL>", "name": "ef7321"}, "rel": null}}}], "meta": {"has_more": false, "after_cursor": "eyJvIjoiY3JlYXRlZF9hdCxpZCIsInYiOiJaRDhsOVdjQUFBQUFhUTJzR00xeUlBQUEifQ==", "before_cursor": "eyJvIjoiY3JlYXRlZF9hdCxpZCIsInYiOiJaQ2dnOVdjQUFBQUFhWTF0aDVkeUlBQUEifQ=="}, "links": {"prev": "https://paguemenossac.zendesk.com/api/v2/tickets/1523112/audits.json?page%5Bbefore%5D=eyJvIjoiY3JlYXRlZF9hdCxpZCIsInYiOiJaQ2dnOVdjQUFBQUFhWTF0aDVkeUlBQUEifQ%3D%3D&page%5Bsize%5D=100", "next": "https://paguemenossac.zendesk.com/api/v2/tickets/1523112/audits.json?page%5Bafter%5D=eyJvIjoiY3JlYXRlZF9hdCxpZCIsInYiOiJaRDhsOVdjQUFBQUFhUTJzR00xeUlBQUEifQ%3D%3D&page%5Bsize%5D=100"}}