

DEV_DOMAIN = "sandbox-api.pagaleve.io"
PRD_DOMAIN  = "api.pagaleve.com.br"
 
AUTH_ENDPOINT =  "https://{domain}/v1/authentication"
VERIFY_CHECKOUT = "https://{domain}/v1/checkouts/{checkout_id}"
VERIFY_PAYMENT = "https://{domain}/v1/payments/{payment_id}"
REQUEST_REFUND = "https://{domain}/v1/payments/{payment_id}/refund"


PAYMENT_STATUS_CAPTURED = "CAPTURED"
REFUND_REASON_REQUEST = "REQUESTED_BY_CUSTOMER"

WITHOUT_JSON_ERROR = "Erro ao pedir estorno"
NOT_MAPPED_ERROR = "Erro não mapeado ao pedir estorno: "

PAYMENT_NOT_FOUND_ERROR = "PaymentNotFoundException"
CHECKOUT_NOT_FOUND_ERROR = "CheckoutNotFoundException" 
SAME_REFUND_REFERENCE_ERROR = "RefundAlreadyExists"
UUID_ERROR = "id must be a UUID"

type_of_standard_erros_and_translated_msg = {
    PAYMENT_NOT_FOUND_ERROR: "Não foi encontrado o pagamento dessa vendo na paga leve",
    CHECKOUT_NOT_FOUND_ERROR: "Checkout da venda não foi encontrado na paga leve",
    UUID_ERROR: "Checkout_id ou payment_id estão incorretos",
    SAME_REFUND_REFERENCE_ERROR: "Já foi realizado um pedido de estorno para esse id_estorno"
}





