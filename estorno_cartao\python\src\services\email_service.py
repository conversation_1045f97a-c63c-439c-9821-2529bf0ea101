import imaplib
import smtplib
import email
import ssl

from email.mime.multipart import MIMEMultipart 
from email.mime.text import MIMEText
from email.mime.base import MIMEBase
from email.mime.image import MIMEImage
from email.header import Header

import os
import re
import base64
import json
from typing import Optional

from datetime import datetime

class EmailService():
    
    def __init__(self, app_config, path_download ,email_provider='outlook'):                                

        # app_config['bot'], app_config['email'], app_config['firefox']['default_download_path']
        self.path_download = path_download
        self.email_address = app_config['bot']['email']
        self.password = app_config['bot']['password']
        self.path_eccomerce = app_config['firefox']['download_path_eccomerce']
        self.path_PDV = app_config['firefox']['download_path_PDV']
        self.path_retorno = app_config['firefox']['download_path_retorno']
        self.path_vtex_manual = app_config['firefox']['download_path_vtex_manual']
        self.path_ligero_poslio = app_config['firefox']['download_path_poslio']
        self.emails = app_config['email']
        self.email_host = self.__get_email_host(email_provider=email_provider)
        
        super().__init__()
        
        
    def __get_email_host(self, email_provider):       
        if email_provider.lower() == 'outlook':
            imap_ssl_host = {'host':'outlook.office365.com', 'port':993}
            smtp_host = {'host':'smtp-mail.outlook.com', 'port':587}
            return [imap_ssl_host, smtp_host]
        elif email_provider.lower() == 'zimbra':
            imap_ssl_host = {'host':'smtp.pmenos.com.br', 'port':465}
            smtp_host = {'host':'smtp.pmenos.com.br', 'port':587}
            return [imap_ssl_host, smtp_host]
        else:
            raise Exception('Provedor invalido')
    

    def send_email(self, email_to, email_body, email_subject, attachment_path=None, att_name=None):
        
        msg = MIMEMultipart()       
        
        # Verificando se existe anexo para ser enviado
        if bool(attachment_path):            
            
            attachment = open(attachment_path, "rb")
            
            att = MIMEBase('application', 'octet-stream') 
            att.set_payload((attachment).read())    
            email.encoders.encode_base64(att)     
            att.add_header('Content-Disposition', "attachment; filename= %s" % att_name)
            msg.attach(att)

        msg.attach(MIMEText(email_body, 'html'))

        msg['From'] = self.email_address
        # msg['From'] = '<EMAIL>'
        msg['To'] = email_to[0]        
        msg['CC'] = ', '.join(email_to[1:])        
        msg['Subject'] = email_subject                                       

        smtp_ssl_host =  self.email_host[1]
        server = smtplib.SMTP(smtp_ssl_host['host'], smtp_ssl_host['port'])
        server.starttls()
        server.login(self.email_address, self.password)
        server.sendmail(self.email_address, email_to, msg.as_string())
        # server.sendmail('<EMAIL>', email_to, msg.as_string())        
        server.quit()    

    def __get_att_paths(self):
        option = self.path_download
        if  option == 0:
            _path = self.path_eccomerce
        
        if option == 1:
            _path = self.path_PDV

        if option == 2:
            _path = self.path_retorno
        
        if option == 3:
            _path = self.path_vtex_manual

        if option == 96:
            _path = self.path_ligero_poslio    

        paths = []
        print('retornou o path', _path)
        for filename in os.listdir(_path):
            paths.append('{}\\{}'.format(_path, filename))            
        return paths
    
    def send_mails_adq(self, email_to:str, email_body:str, email_subject:str):                       
        for _path in self.__get_att_paths():
            name = _path.split('\\')[2]
            self.send_email(email_to, email_body, email_subject, attachment_path=_path, att_name=name)
            os.remove(_path)
  

    def retrieve_messages(self, email_from, email_subject= None, unseen= False, origin= 'Inbox'):

        msg_payloads = []
        imap_host = self.email_host[0]
        
        UNSEEN = '' if not unseen else '(UNSEEN) '
        EMAIL_FROM = '' if email_from is None else '(FROM {}) '.format(email_from)
        EMAIL_SUBJECT = '' if email_subject is None else '(SUBJECT "{}")'.format(email_subject)

        search_params = '({}{}{})'.format(UNSEEN, EMAIL_FROM, EMAIL_SUBJECT)
        search_params = 'ALL' if search_params == '()' else search_params

        # Conexao com o servidor de email
        mail = imaplib.IMAP4_SSL(imap_host['host'], imap_host['port'])
        mail.login(self.email_address, self.password)
        mail.select(origin)        
        data = mail.search(None, search_params.replace(') )', '))'))[1]
        mail_ids = data[0]
        id_list = mail_ids.split()

        for mail_id in id_list:
            mail_data = mail.fetch(mail_id, '(RFC822)')[1]            
            for response_part in mail_data:            
                if isinstance(response_part, tuple):
                    msg = email.message_from_string(list(response_part)[1].decode('utf-8'))                
                    print(msg['subject']); print(msg['from'])                    
                    msg_aux= str(msg.get_payload()[0])

                    msg_aux= msg_aux.replace('Content-Type: text/html; charset=utf-8', '')   
                    msg_aux = msg_aux.replace('Content-Transfer-Encoding: base64\n', '')                      
                    
                    msg_payloads.append(str(base64.b64decode(msg_aux))) 
                    
                    # Baixando anexo, se houver       
                    option = self.path_download

                    if option == 0:
                        
                        for part in msg.walk():
                            if part.get_content_maintype() == 'multipart': continue
                            if part.get('Content-Disposition') is None: continue
                            fileName = part.get_filename()+str(mail_id)
                            if bool(fileName):
                                filePath = os.path.join(self.path_eccomerce+'\\', fileName)
                                if not os.path.isfile(filePath):
                                    fp = open(filePath, 'wb')
                                    fp.write(part.get_payload(decode=True))
                                    fp.close()            
                    if option == 1:
    
                        for part in msg.walk():
                            if part.get_content_maintype() == 'multipart': continue
                            if part.get('Content-Disposition') is None: continue
                            fileName = part.get_filename()+str(mail_id)
                            if bool(fileName):
                                filePath = os.path.join(self.path_PDV+'\\', fileName)
                                if not os.path.isfile(filePath):
                                    fp = open(filePath, 'wb')
                                    fp.write(part.get_payload(decode=True))
                                    fp.close()           
                    if option == 2:
    
                        for part in msg.walk():
                            if part.get_content_maintype() == 'multipart': continue
                            if part.get('Content-Disposition') is None: continue
                            fileName = part.get_filename()+str(mail_id)

                            #Tratando dados caso venha xlsb
                            fileName = fileName.split('.')
                            extensao = fileName[1]
                            extensao = extensao.replace(extensao, '.xls')
                            fileName = fileName[0]+datetime.now().strftime('_%d%M%Y%S%f')+extensao

                            if bool(fileName):
                                filePath = os.path.join(self.path_retorno+'\\', fileName)
                                if not os.path.isfile(filePath):
                                    fp = open(filePath, 'wb')
                                    fp.write(part.get_payload(decode=True))
                                    fp.close()           
        mail.logout()
        return msg_payloads



