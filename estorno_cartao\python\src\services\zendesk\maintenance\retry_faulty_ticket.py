from asyncio.log import logger
from calendar import day_abbr
from pathlib import Path
import sys
from datetime import datetime

def local_execution_with_others_projects_modules(current_file: str, directory_to_go_back: int = 1) -> Path:
    """
    If in a local execution the current module uses resources from another \n
    module that is in the same project i.e.(this module is in the same \n
    directory as the other module) and they aren't in the same level. \n
    So you need to add the common dictory between the modules to the Paths of execution \n
    
    Args:
        `current_file` (str): file path who calls the other modules

    Returns:
        `Path`: common directory Path
    """
    
    src_folder = Path(current_file.split(":")[0].upper() + ":" + current_file.split(":")[1])
    for _ in range(directory_to_go_back): src_folder = src_folder.parent
    if src_folder.__str__() not in sys.path: sys.path.append(str(src_folder))
    if Path.cwd().__str__() not in sys.path: sys.path.append(Path.cwd().__str__())

    return src_folder

if __name__ == "__main__":
    src_folder = local_execution_with_others_projects_modules(__file__, 4)

from services.logger import create_logger
from services.zendesk.zen_services import create_zendesk_ticket
from Connections.search_db import SearchDb
from Connections.update_db import UpdateDb
from models.refund_log import estorno_venda_log_orm
    


if __name__ == '__main__': 
    retry_ticket_logger = create_logger('')
    retry_ticket_logger.setLevel(10)
else: 
    retry_ticket_logger = create_logger(__name__, without_handler = True)
    

if __name__ == "__main__":

    finder = SearchDb()
    changer = UpdateDb()
    
    ids_estorno = (323666,
                    324106,
                    324415,
                    324435)

    for id_estorno in ids_estorno:

        refund_info_raw = finder.search_refund_by_refund_id(id_estorno)
        refund_info = estorno_venda_log_orm(refund_info_raw[0][0])

        zen_ticket_res = create_zendesk_ticket(refund_info, env = "prd")

        if 'error' in zen_ticket_res.data:
            logger.error(f"Error na criação de ticket. Id_Estorno {id_estorno}")
            
        ticket_id = zen_ticket_res.data["audit"]["ticket_id"]
        changer.record_zendesk_ticket_id(id_estorno, "MANUTEÇÃO",ticket_id)

    
        
    
    print("fim")



