import requests
import json

class PlanoAssinatura():

    def auth_token():
        url = "https://prod.apipmenos.com/authorizer/v1/token"
        
        payload = {}
        headers = {
        'x-api-key': 'eAbIrO2wGsahJ3cSZAJOK6qPbqyYpV83762XgXaB',
        'Authorization': 'UGFpbmVsVmVuZGFzOjl5Vmh1NHc2eVJxanZkZlY'
        }

        response = requests.request("POST", url, headers=headers, data=payload) 
        access_token = json.loads(response.text)['access_token']
        
        return access_token


    def check_assinatura(N_assinaturas,auth_token):
        
        url = "https://prod.apipmenos.com/assinatura-digital/v1/assinaturas/prevenda/"+N_assinaturas+"/estornar"

        payload = {}
        headers = {
        'x-api-key': 'CoAQMBN92w9srsPKk5nmd3zw6xbiCNhe6zcVysjK',
        'access_token': auth_token
        }

        response = requests.request("PUT", url, headers=headers, data=payload)

        return response.text