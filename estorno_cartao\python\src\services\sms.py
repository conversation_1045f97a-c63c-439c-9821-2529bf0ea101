import requests
import json


class SMS():

    def __init__(self, config):
        self.config = config
        self.__api_base_url = config['api']['sms']['api_base_url']   
        self.token = config['api']['sms']['token']  
        self.interaction = config['api']['sms']['interaction']                      
        super().__init__()   


    def obtertoken(self):
        url = self.__api_base_url.format(self.token)
        print(url)
        payload = json.dumps({
            "grant_type": "client_credentials",
            "client_id": "0ygeonqh09gv66nc1l2srpka",
            "client_secret": "8JtrE384m209tN3PZrUYN7Bf",
            "account_id": "********"
        })
        headers = {
        'Content-Type': 'application/json'
        }

        response = requests.request("POST", url, headers=headers, data=payload)
        print(response.text)
        

        return response.json()['access_token']

    def sendsms(self,telefone,clienteSempre,valorestorno):
        
        url = self.__api_base_url.format(self.interaction)
        telefone = '55'+telefone

        texto = 'FarmaciaPagueMenos: Seu Pix no valor de R${} foi estornado.'.format(valorestorno)

        payload = json.dumps({
            "ContactKey": "ID_CLIENTE",
            "EventDefinitionKey": "APIEvent-a98806f8-10f3-641d-fb62-16a687a85245",
            "Data": {
                "ID_Cliente": clienteSempre,
                "Telefone": telefone,
                "TipoEnvio": "shortOne",
                "Mensagem": texto
            }
        })

        print(payload)
        
        token = self.obtertoken()

        headers = {
        'Authorization': 'Bearer '+token,
        'Content-Type': 'application/json'
        }

        response = requests.request("POST", url, headers=headers, data=payload)
        print('#######DEBUG#######')    
        print(response.text)
        print('#######DEBUG#######')

        if response.ok:
            print("sms_enviado")
        else:
            print("sms não enviado")    
