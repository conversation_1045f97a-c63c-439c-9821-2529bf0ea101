import pytest
from unittest.mock import patch
from src.executers.main_tef_extrafarma import TEFEX
from src.executers.main_pos_extrafarma import POSEX
from src.executers.main_tef import TEF
from src.executers.main_pos import POS
from src.utils import get_config
from src.models.result_handle import ResultHandle


executors = {
    "tef_exf": TEFEX(get_config()),
    "tef": TEF(get_config()),
    "pos_exf": POSEX(get_config()),
    "pos": POS(get_config())
}


@pytest.mark.parametrize("executor_name", list(executors.keys()))
@patch('src.executers.main_tef_extrafarma.RefundApi.get_not_processed_refunds_by_days_back_and_pos_kind')
def test_error_flow_exiting(mock_RefundApi, executor_name):
    """Testar fluxo de erro quando ocorrer algum erro na requisição de novos estornos

    Resultado esperado: 
        - Lançar exceção SystemExit
    """
    mock_RefundApi.return_value = ResultHandle.Fail("Erro na requisição para verificar novos estornos", None)

    with pytest.raises(SystemExit) as e: 
        executors[executor_name].execute_process()
    assert e.type is SystemExit, f"Condição de erro não esperada {e.type}. Esperado SystemExit"

@pytest.mark.parametrize("executor_name", list(executors.keys()))
@patch('src.executers.main_tef_extrafarma.RefundApi.get_not_processed_refunds_by_days_back_and_pos_kind')
def test_error_flow_in_orm_of_one_refund(mock_RefundApi, executor_name):
    """Testar fluxo de erro quando ocorrer algum erro na ORM de pelo menos um dos estorno

    Resultado esperado: 
        - Não lançar exceção
        - Registrar log de warning
        - Enviar mensagem para o telegram
        - Retornar 0
    """

    mock_RefundApi.return_value = ResultHandle.Ok([ResultHandle.Fail("Simulando erro na ORM de apenas um estorno", {'ID_ESTORNO': 1234567890}),])

    
    result = executors[executor_name].execute_process()

    assert result == 0, f"Resultado esperado era 0, porém o obtido foi {result}"
    

if __name__ == '__main__':
    pytest.main([__file__])
