from typing import Dict, <PERSON>, Literal, <PERSON><PERSON>
import sys
from pathlib import Path


def local_execution_with_others_projects_modules(current_file: str, directory_to_go_back: int = 1) -> Path:
    """
    If in a local execution the current module uses resources from another \n
    module that is in the same project i.e.(this module is in the same \n
    directory as the other module) and they aren't in the same level. \n
    So you need to add the common dictory between the modules to the Paths of execution \n
    
    Args:
        `current_file` (str): file path who calls the other modules

    Returns:
        `Path`: common directory Path
    """
    
    src_folder = Path(current_file.split(":")[0].upper() + ":" + current_file.split(":")[1])
    for _ in range(directory_to_go_back): src_folder = src_folder.parent
    if src_folder.__str__() not in sys.path: sys.path.append(str(src_folder))
    if Path.cwd().__str__() not in sys.path: sys.path.append(Path.cwd().__str__())

    return src_folder

if __name__ == "__main__":
    src_folder = local_execution_with_others_projects_modules(__file__, 3)


from services.vtex_api_service import VTEXHandler
from utils import verify_diff_values, get_config, DiffValueError
import Config.vars as vars




class VtexError(Exception):
    def __init__(self, *args: object) -> None:
        super().__init__(*args)

class VtexValidator:

    def __init__(self, obj_estorno_log: Dict) -> None:

        self.obj_estorno_log = obj_estorno_log
    
    def get_vtex_info(self)-> Union[Tuple[Literal[-1], Dict], Tuple[Dict, Dict]]:

        app_config = get_config()
        vtex_service = VTEXHandler(app_config['api']['vtex']) 
        dados_vtex = vtex_service.retrieve_data(self.obj_estorno_log) 
        
        try:
            self._is_a_valid_vtex_num(dados_vtex)
            self._is_there_authorizations(dados_vtex)
            self._have_sale_been_cancelled(dados_vtex)
            self._is_a_sale_in_cash(dados_vtex)
            self._is_a_pm_sale(dados_vtex)
            self._do_values_match(dados_vtex)

        except VtexError:
            return -1 , self.obj_estorno_log

        except Exception as error:
            msg = "Erro não mapeado nas validações da VTEX"
            print(f"{msg}: {error}") 
            self.obj_estorno_log['motivo'] =  msg
            self.obj_estorno_log['status'] = vars.STATUS_PENDENCY_FOR_CSC  
            return -1 , self.obj_estorno_log

        self.obj_estorno_log["tid"] = dados_vtex["t_id"]

        return dados_vtex, self.obj_estorno_log
    
    def _is_a_valid_vtex_num(self, dados_vtex: Dict):

        
        if dados_vtex == -1:
            msg = 'Codigo da VTEX invalido' 
            self.obj_estorno_log['motivo'] = msg
            self.obj_estorno_log['status'] = vars.STATUS_PENDENCY_FOR_CSC   
            raise VtexError()

    def _is_there_authorizations(self, dados_vtex: Dict):

        if dados_vtex == -2:
            msg = 'Codigos de autorizacao nao encontrados'
            self.obj_estorno_log['motivo'] = msg
            self.obj_estorno_log['status'] = vars.STATUS_PENDENCY_FOR_CSC    
            raise VtexError()
    
    def _have_sale_been_cancelled(self, dados_vtex: Dict):

        if dados_vtex == -3:
            msg = 'Compra cancelada na VTEX'
            self.obj_estorno_log['motivo'] = msg
            self.obj_estorno_log['status'] = vars.STATUS_PENDENCY_FOR_CSC
            raise VtexError()
    
    def _is_a_sale_in_cash(self, dados_vtex: Dict):

        if dados_vtex == -4:
            msg =  'Compra feita a dinheiro'
            self.obj_estorno_log['motivo'] = msg
            self.obj_estorno_log['status'] = vars.STATUS_REFUND_PROCESS_END     
            raise VtexError()  
            
    def _is_a_pm_sale(self, dados_vtex: Dict):

        if dados_vtex == -8:
            msg =  'Nao se trata de uma venda Pague Menos'
            self.obj_estorno_log['motivo'] = msg
            self.obj_estorno_log['status'] = vars.STATUS_PENDENCY_FOR_CSC    
            raise VtexError()
    
    def _do_values_match(self, dados_vtex: Dict): 
        

        try:
            obj_estorno_log = verify_diff_values(self.obj_estorno_log, dados_vtex["valor"])
            
        except DiffValueError as error:
            
            self.obj_estorno_log['motivo'] = error.__str__()
            self.obj_estorno_log['status'] = vars.STATUS_PENDENCY_FOR_CSC 
            raise VtexError()

        self.obj_estorno_log["valorEstorno"] = obj_estorno_log["valorEstorno"]

        


if __name__ == "__main__":


    

    obj_estorno_log = {
        "bandeira":"MasterCard",
        "valorTotalVenda": 98.47,
        "valorEstorno": 49.49,
        "numeroPedidoVTEX": "146454184706901"
    }

    try:

        app_config = get_config()
        vtex_referee  = VtexValidator(obj_estorno_log)
        vtex_sale_info, obj_estorno_log = vtex_referee.get_vtex_info()
        print("fim")

    except Exception as e :
        print(f"Erro: {e}")

