import requests
import json
from Connections.search_db import SearchDb
import Config.vars as vars
from pathlib import Path
import sys
from utils import verify_diff_values, DiffValueError

class ItauService:
    def __init__(self, config: dict):
        self.config = config
        self.__api_url_estorno = config['itau']['api_estorno_url']
        self.__api_url_consulta = config['itau']['api_consulta_url']
        self.__api_url_consulta_estorno = config['itau']['api_consulta_estorno_url']        
        self.__api_url_token = config['itau']['api_token_url']
        self.client_id_pm = config['itau']['client_id_pm']
        self.client_secret_pm = config['itau']['client_secret_pm']
        self.client_id_ef = config['itau']['client_id_ef']
        self.client_secret_ef = config['itau']['client_secret_ef']
        self.search = SearchDb()
        self.tokens = {'Pague Menos': None, 'Extrafarma': None}

    def estornoitau(self, id_estorno, nome_empresa, obj_estorno_log):
        try:
            idpix = self.search.search_id_pix(id_estorno)
            if idpix is None:
                return self.atualizar_obj_estorno_log(obj_estorno_log, vars.ResultHandle.Fail('Não foi possível localizar o Id Pix'), idpix)
            
            certificado = self.selecionar_certificado(nome_empresa)

            if self.tokens[nome_empresa] is None:
                self.tokens[nome_empresa] = self.obtertoken(nome_empresa, certificado)

            url_consulta = self.__api_url_consulta.format(idpix)
            response = self.realizar_requisicao(url_consulta, nome_empresa, 'get', cert=certificado)

            if response is None or response.status_code != 200:
                return self.atualizar_obj_estorno_log(obj_estorno_log, vars.ResultHandle.Fail('Erro ao consultar o Pix'), idpix)

            valor_pix = float(response.json().get('valor'))

            try:
                obj_estorno_log = verify_diff_values(obj_estorno_log, valor_pix)
            except DiffValueError as e:
                return self.atualizar_obj_estorno_log(obj_estorno_log, vars.ResultHandle.Fail('Valor da venda e o valor do Pix não conferem'), idpix)
            
            body = {'valor': obj_estorno_log["valorEstorno"]}
            url_estorno = self.__api_url_estorno.format(idpix, id_estorno)  
            response = self.realizar_requisicao(url_estorno, nome_empresa,'put', nome_empresa, json.dumps(body), cert=certificado)
            
            if response is None:
                return self.atualizar_obj_estorno_log(obj_estorno_log, vars.ResultHandle.Fail('Erro ao estornar o Pix'), idpix)

            if response.status_code == 400:
                violacoes = response.json().get('violacoes')
                razao_error = violacoes[0].get('razao')
                if 'Já existe uma devolucao com o identificador cadastrado' in razao_error:
                    url_consulta_estorno = self.__api_url_consulta_estorno.format(idpix,id_estorno)
                    response_estorno = self.realizar_requisicao(url_consulta_estorno, nome_empresa, 'get', cert=certificado)
                    result = self.tratar_resposta_estorno(response_estorno)
                    return self.atualizar_obj_estorno_log(obj_estorno_log, result, idpix)
                elif 'Devolução não pode ser efetivada quando o valor pago já foi integramelnte devolvido' in razao_error:
                    return self.atualizar_obj_estorno_log(obj_estorno_log, vars.ResultHandle.Fail('Valor pix já foi estornado de forma integral'), idpix)
                else:
                    result = self.tratar_resposta_estorno(response)
                    return self.atualizar_obj_estorno_log(obj_estorno_log, result, idpix)

            result = self.tratar_resposta_estorno(response)
            return self.atualizar_obj_estorno_log(obj_estorno_log, result, idpix)

        except Exception as e:
            print(f"Detalhe do erro: {e}")
            result = vars.ResultHandle.Fail("Erro ao pedir estorno na API")
            return self.atualizar_obj_estorno_log(obj_estorno_log, result, idpix)
  
    def tratar_resposta_estorno(self, response) -> vars.ResultHandle:
        if response.status_code in (200, 201):
            status, motivo = response.json().get('status'), response.json().get('motivo')
            if status == 'DEVOLVIDO':
                return vars.ResultHandle.Ok(motivo)
            elif status == 'NAO_REALIZADO':
                return vars.ResultHandle.Fail(motivo)
            elif status == 'EM_PROCESSAMENTO':
                return vars.ResultHandle.Waiting('Pix Itaú em processamento', motivo)
            else:
                return vars.ResultHandle.Fail('Erro na API: Status não mapeado')

        status_message = {
            400: "Requisição com formato inválido",
            403: "Requisição de participante autenticado que viola alguma regra de autorização",
            404: "Recurso solicitado não foi encontrado",
            422: "Erro no processamento da requisição recebida",
            503: "Serviço indisponível",
        }.get(response.status_code, 'Erro desconhecido na API')

        return vars.ResultHandle.Fail(status_message)
        
    def atualizar_obj_estorno_log(self, obj_estorno_log, result, idpix, status_sucesso=vars.STATUS_REFUND_PROCESS_END, status_espera=vars.STATUS_WAIT_ITAU, status_erro=vars.STATUS_PENDENCY_FOR_CSC):
        
        if result.success:
            motivo = 'REALIZADO'
            status = status_sucesso

        elif result.wait:
            motivo = 'Estorno pix Itaú em processamento'
            status = status_espera
            
        else:
            motivo = result.error_description
            status = status_erro

        obj_estorno_log['codigoAutorizacao'] = obj_estorno_log['idEstorno']
        obj_estorno_log['motivo'] = motivo
        obj_estorno_log['status'] = status
        #obj_estorno_log['idpix'] = idpix

        return obj_estorno_log
    
    def obtertoken(self, nome_empresa, certificado) -> str:
        try:    
            client_id, client_secret = (self.client_id_ef, self.client_secret_ef) if nome_empresa == 'Extrafarma' else (self.client_id_pm, self.client_secret_pm)

            data = {
                'client_id': client_id,
                'client_secret': client_secret,
                'grant_type': 'client_credentials'
            }
            headers = {'Content-Type': 'application/x-www-form-urlencoded'}
            response = requests.post(self.__api_url_token, data=data, headers=headers, cert=certificado)
            return response.json().get('access_token') if response.status_code == 200 else None

        except Exception as e:
            print(f'Erro inesperado ao obter token: {str(e)}')
            return None    
        
    def criar_headers(self, nome_empresa):
        client_id = (self.client_id_ef) if nome_empresa == 'Extrafarma' else (self.client_id_pm)
        headers = {
            'Authorization': f'Bearer {self.tokens[nome_empresa]}',
            'Content-Type': 'application/json',
            'x-itau-apikey': client_id
        }
        return headers
    def realizar_requisicao(self, url, nome_empresa, method='get', headers=None, data=None, cert=None):
        try:
            headers = self.criar_headers(nome_empresa)
            if method == 'get':
                response = requests.get(url, headers=headers, cert=cert)
            elif method == 'put':
                response = requests.put(url, headers=headers, data=data, cert=cert)
            
            if response.status_code == 403:
                self.tokens[nome_empresa] = self.obtertoken(nome_empresa, cert)
                headers['Authorization'] = f'Bearer {self.tokens[nome_empresa]}'
                return self.realizar_requisicao(url, method, headers, data, cert)

            return response
        except Exception as e:
            print(f"Erro ao realizar a requisição: {e}")
            return None

    def selecionar_certificado(self, nome_empresa: str) -> tuple:
        src_folder = local_execution_with_others_projects_modules(__file__, 3)
        certificados = {
            'Pague Menos': (src_folder.__str__() + r'\certificate\certificado_pm.crt', src_folder.__str__() + r'\certificate\chave_pm.key'),
            'Extrafarma': (src_folder.__str__() + r'.\certificate\certificado_ef.crt', src_folder.__str__() + r'.\certificate\chave_ef.key'),
        }       
        return certificados.get(nome_empresa, (None, None))
   
    
def local_execution_with_others_projects_modules(current_file: str, directory_to_go_back: int = 1) -> Path:
    """
    If in a local execution the current module uses resources from another \n
    module that is in the same project i.e.(this module is in the same \n
    directory as the other module) and they aren't in the same level. \n
    So you need to add the common dictory between the modules to the Paths of execution \n
    
    Args:
        `current_file` (str): file path who calls the other modules

    Returns:
        `Path`: common directory Path
    """
    
    src_folder = Path(current_file.split(":")[0].upper() + ":" + current_file.split(":")[1])
    for _ in range(directory_to_go_back): src_folder = src_folder.parent
    if src_folder.__str__() not in sys.path: sys.path.append(str(src_folder))
    if Path.cwd().__str__() not in sys.path: sys.path.append(Path.cwd().__str__())

    return src_folder