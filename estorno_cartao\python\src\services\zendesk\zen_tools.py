import locale
from requests.models import Response
from typing import Dict, Iterable
from pathlib import Path
from time import perf_counter
from functools import wraps
import sys

def local_execution_with_others_projects_modules(current_file: str, directory_to_go_back: int = 1) -> Path:
    """
    If in a local execution the current module uses resources from another \n
    module that is in the same project i.e.(this module is in the same \n
    directory as the other module) and they aren't in the same level. \n
    So you need to add the common dictory between the modules to the Paths of execution \n
    
    Args:
        `current_file` (str): file path who calls the other modules

    Returns:
        `Path`: common directory Path
    """
    
    src_folder = Path(current_file.split(":")[0].upper() + ":" + current_file.split(":")[1])
    for _ in range(directory_to_go_back): src_folder = src_folder.parent
    if src_folder.__str__() not in sys.path: sys.path.append(str(src_folder))
    if Path.cwd().__str__() not in sys.path: sys.path.append(Path.cwd().__str__())

    return src_folder

if __name__ == "__main__":
    src_folder = local_execution_with_others_projects_modules(__file__, 3)

from services.zendesk import zen_var
from services.logger import create_logger
from utils import ResultHandle


if __name__ == '__main__': 
    zen_tools_logger = create_logger('')
    zen_tools_logger.setLevel(10)
else: 
    zen_tools_logger = create_logger(__name__, without_handler = True)



def validate_request(api_res: Response) -> ResultHandle:
    """Validar reposta da api

    Args:
        api_res (Response): Resposta da API

    Returns:
        ResultHandle: Objesto de resultado
    """
    
    if api_res.status_code in zen_var.request_error_and_standard_msg:
        msg = zen_var.request_error_and_standard_msg[api_res.status_code]
        return make_request_result(api_res, True, msg)

    if api_res.status_code not in (200, 201):
        return make_request_result(api_res, True, "Resposta de API não foi mapeada")

    return make_request_result(api_res, False)


def make_request_result(api_res: Response, is_error: bool, msg: str = None) -> ResultHandle:
    """Criar objeto de resultado para requisição

    Args:
        api_res (Response): resultado da API
        is_error (bool): Indica se houve erro na requisição
        msg (str, optional): Mensagem do resultado

    Returns:
        ResultHandle: Objeto de resultado
    """
    body_response: Dict = api_res.json()


    if "error" in body_response and msg is None:

        if "message" in body_response["error"]:
            msg = body_response["error"]["message"]
        


    msg = shrink_message(msg)
    if is_error: 
        zen_tools_logger.error(msg)
        return ResultHandle.Fail(msg, body_response)

    return ResultHandle.Ok(body_response)


def shrink_message(msg: str, max_lenght_of_chars: int = 254) -> str:
    """Cortar a messagem de modo a não dar erro de overflow no banco

    Args:
        msg (str): String da menssagem 
        max_lenght_of_chars (int, optional): Tamanho máximo da string

    Returns:
        str: Mensagem cortada
    """

    if msg is not None and len(msg) > max_lenght_of_chars:
        msg = msg[:254]
        return msg
    else:
        return msg


def make_auth(config: Dict, env: str):
    """Criar Objeto de auth

    Args:
        config (Dict): Objeto com dados de configuração 
        env (str): Para qual ambiente será configurado
    Returns:
        Dict: Dicionário com os headers
    """

    auth = (f'{config["api"]["zendesk"][env]["user_email"]}/token', 
            config["api"]["zendesk"][env]["token"])

    return auth



def timer(func):

    @wraps(func)
    def wrapper_timer(*args, **kwargs):

        start_time = perf_counter()
        value = func(*args, **kwargs)
        end_time = perf_counter()

        run_time = end_time - start_time

        zen_tools_logger.debug(f"Fim da {func.__name__}() em {run_time:.6f} secs")
        return value

    return wrapper_timer


def extract_branch_from_name(name: str) -> str:
    branch = name.split("-")[0]
    return branch

BRANCH_TREATMENT_CODE = 1

item_treatment_functions = {
    BRANCH_TREATMENT_CODE: extract_branch_from_name
}


@timer
def binary_search(arr: Iterable, target: str, case_code: int = 0):

    if not isinstance(target, str):
        target = str(target)

    left, right = 0, len(arr) - 1

    if case_code != 0:
        func_treat = item_treatment_functions[case_code]

    while left <= right:

        mid = (left + right) // 2

        if case_code != 0: item = func_treat(arr[mid]["name"])
        else: item = arr[mid]["name"]

        if item == target:
            return mid

        elif item < target:
            left = mid + 1

        else:
            right = mid - 1

    return -1

@timer
def linear_search(arr: Iterable, target: str, case_code: int = 0):

    if not isinstance(target, str):
        target = str(target)
    
    if case_code != 0:
        func_treat = item_treatment_functions[case_code]
    
    for index, raw_item in enumerate(arr):

        if case_code != 0: item = func_treat(raw_item["name"])
        else: item = raw_item["name"]

        if item == target: return index
    
    return -1


def ensure_search(arr: Iterable, target: str, case_code: int = 0) -> int:
    """Faz uma pesquisa binário, caso não ache nada ele faz uma  
    pesquisa linear

    Args:
        arr (Iterable): Elemento percurrível \n
        target (str): Elemento a ser encontrado \n
        case_code (int, optional): Caso de tratamento para o \n
        item está contido no Elemento percurrível. Se for zero não
        há tratamento

    Returns:
        int: posição do item alvo na lista
    """

    option_index = binary_search(arr, target, case_code)

    if option_index == -1: 
        option_index = linear_search(arr, target, case_code)

    return option_index


def format_currency_value(value: float)-> str:

    locale.setlocale(locale.LC_ALL, 'pt_BR.UTF-8')
    formatted_value = locale.format_string("%.2f", value, grouping = True)
    locale.setlocale(locale.LC_ALL, '')
    return formatted_value



