from pathlib import Path
import requests
import sys
from typing import <PERSON><PERSON>, Dict
from urllib3.connectionpool import log as urllib3_logger
from botcity.maestro import BotMaestroSDK

def local_execution_with_others_projects_modules(current_file: str, directory_to_go_back: int = 1) -> Path:
    """
    If in a local execution the current module uses resources from another \n
    module that is in the same project i.e.(this module is in the same \n
    directory as the other module) and they aren't in the same level. \n
    So you need to add the common dictory between the modules to the Paths of execution \n
    
    Args:
        `current_file` (str): file path who calls the other modules

    Returns:
        `Path`: common directory Path
    """
    
    src_folder = Path(current_file.split(":")[0].upper() + ":" + current_file.split(":")[1])
    for _ in range(directory_to_go_back): src_folder = src_folder.parent
    if src_folder.__str__() not in sys.path: sys.path.append(str(src_folder))
    if Path.cwd().__str__() not in sys.path: sys.path.append(Path.cwd().__str__())

    return src_folder

if __name__ == "__main__":
    src_folder = local_execution_with_others_projects_modules(__file__, 3)

import utils
from Config.vars import PAGUE_MENOS_CASE, EXTRA_FARMA_CASE, PAYMENT_METHOD_FULLY_REFUNDED_REASON
from services.logger import create_logger
from models.refund_log import EstornoLog
from services.pagaleve import paga_leve_vars
from services.pagaleve import paga_leve_tools

urllib3_logger.setLevel(20)
if __name__ == '__main__': 
    paglev_requests_logger = create_logger('')
    paglev_requests_logger.setLevel(10)
else: 
    paglev_requests_logger = create_logger(__name__, without_handler = True)



@utils.exception_report
def get_token(enterprise_code: str) -> utils.ResultHandle:

    config = utils.get_config()

    bot_maestro = BotMaestroSDK()

    bot_maestro.login(
        server=config["botcity"]["server_botcity"],
        login=config["botcity"]["login_botcity"],
        key=config["botcity"]["key_botcity"],
    )

    url = paga_leve_vars.AUTH_ENDPOINT.format(domain = paga_leve_vars.PRD_DOMAIN)

    body = {
        "username": None,
        "password": None
    } 

    cred_label = config["pagaleve"]["prd"]["label"]
    if enterprise_code == PAGUE_MENOS_CASE:

        body["username"] = bot_maestro.get_credential(label = cred_label,
                                                      key= config["pagaleve"]["prd"][PAGUE_MENOS_CASE]["username"])

        body["password"] = bot_maestro.get_credential(label = cred_label,
                                                      key= config["pagaleve"]["prd"][PAGUE_MENOS_CASE]["password"])

    elif enterprise_code == EXTRA_FARMA_CASE:

        body["username"] = bot_maestro.get_credential(label = cred_label,
                                                      key= config["pagaleve"]["prd"][EXTRA_FARMA_CASE]["username"])

        body["password"] = bot_maestro.get_credential(label = cred_label,
                                                      key= config["pagaleve"]["prd"][EXTRA_FARMA_CASE]["password"])
    
    else:

        msg = f"Código da empresa |{enterprise_code}| não é válido"
        paglev_requests_logger.critical(msg)
        raise NotImplementedError(msg)
    
    headers = {
	"Content-Type": "application/json",
    }

    response = requests.post(url, headers=headers, json=body)

    result = paga_leve_tools.validate_request(response)
    if result.failure:
        paglev_requests_logger.error(f"Erro na requisição token")
        return result

    return result

@utils.exception_report
def get_checkout(checkout_id: str, enterprise_code: str) -> utils.ResultHandle:
    """Obter dados de sessão de pagamento na pagaleve

    Args:
        checkout_id (str): ID da sessão de compra na pagaleve
        enterprise_code (str): _description_

    Returns:
        utils.ResultHandle: resultado da requisição
    """

    url = paga_leve_vars.VERIFY_CHECKOUT.format(domain = paga_leve_vars.PRD_DOMAIN,
                                                checkout_id = checkout_id)

    token_info = get_token(enterprise_code)
    if token_info.failure: return token_info

    
    headers = {
	"Content-Type": "application/json",
    "Authorization": "Bearer " + token_info.data["token"]
    }

    response = requests.get(url, headers=headers)

    result = paga_leve_tools.validate_request(response)
    if result.failure:
        paglev_requests_logger.error(f"Erro na verificação do checkout: {checkout_id}")
        return result

    return result

@utils.exception_report
def get_payment(payment_id: str, enterprise_code: str) -> utils.ResultHandle:
    """Obter informações sobre o pagamento

    Args:
        payment_id (str): id do pagamento
        enterprise_code (str): código da empresa: `pm` ou `exf`

    Returns:
        utils.ResultHandle: Resultado da requisição
    """

    url = paga_leve_vars.VERIFY_PAYMENT.format(domain = paga_leve_vars.PRD_DOMAIN,
                                                payment_id = payment_id)

    token_info = get_token(enterprise_code)
    if token_info.failure: return token_info
    
    headers = {
	"Content-Type": "application/json",
    "Authorization": "Bearer " + token_info.data["token"]
    }

    response = requests.get(url, headers=headers)

    result = paga_leve_tools.validate_request(response)
    if result.failure:
        paglev_requests_logger.error(f"Erro na verificação do payment: {payment_id}")
        return result

    return result

def request_refund(payment_info: Dict, refund_info: EstornoLog, enterprise_code: str) -> utils.ResultHandle:

    token_info = get_token(enterprise_code)
    if token_info.failure: return token_info
    
    headers = {
	"Content-Type": "application/json",
    "Authorization": "Bearer " + token_info.data["token"]
    }

    body = {
    "amount": int(refund_info.valorEstorno * 100),
    "description": f"id_estorno: {refund_info.idEstorno}; id_devtroc: {refund_info.idDevtrocaCab}",
    "reference": f"id_estorno: {refund_info.idEstorno}",
    "reason": paga_leve_vars.REFUND_REASON_REQUEST,
    "recipients": payment_info['recipients']
    }

    url = paga_leve_vars.REQUEST_REFUND.format(domain = paga_leve_vars.PRD_DOMAIN,
                                                payment_id = payment_info["id"])
    
    response = requests.post(url, headers=headers, json=body)

    result = paga_leve_tools.validate_request(response)
    if result.failure:
        paglev_requests_logger.error(f"Erro ao fazer pedido de estorno na pagaleve payment_id: {payment_info['id']}")
        return result

    return result


def has_sale_been_fully_refunded(payment_info: Dict) -> utils.ResultHandle:

    if len(payment_info["authorization"]["refunds"]) == 0: 
        return utils.ResultHandle.Fail("Nenhuma estorno realizado")

    total_refunds = sum([refund["amount"] for refund in payment_info["authorization"]["refunds"]])

    if (payment_info["authorization"]["amount"] - total_refunds) == 0: 
        return utils.ResultHandle.Ok(payment_info, PAYMENT_METHOD_FULLY_REFUNDED_REASON)
    
    return utils.ResultHandle.Fail("Ainda existem valores para serem estornados")
       
        

        

if __name__ == "__main__":


    x = get_token("pm")


    print("fim")