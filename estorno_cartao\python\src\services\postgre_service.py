import psycopg2

class PostgreService():

    def __init__(self, sql):                         
        self.sql_execute = sql        
        super().__init__() 

    def sql_executor(self):
        con = psycopg2.connect(host='************', database='bpm', user='usrbpm', password='usrbpm2019')
        # con = psycopg2.connect(host='************', database='bpm', user='postgres', password='postgres')
        cur = con.cursor()
        cur.execute(self.sql_execute)
        con.commit()
        con.close()

    def sql_executor_finish(self):
        con = psycopg2.connect(host='************', database='bpm', user='usrbpm', password='usrbpm2019')
        # con = psycopg2.connect(host='************', database='bpm', user='postgres', password='postgres')
        cur = con.cursor()
        cur.execute(self.sql_execute)
        recset = cur.fetchall()
        id_return = ''

        for id_ in recset:
            id_returnex = list(id_)
            id_return = id_returnex[0]

        con.commit()
        con.close()

        return str(id_return)

