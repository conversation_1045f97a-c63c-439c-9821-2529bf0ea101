"""Classe que, a partir das configs do projeto, implementa a criação de conexões e o fechamento das mesmas"""
import pyodbc
from Config.vars import ENV, CONN

class DAO:

    def __init__(self):
        pass

    def create_connection(self, db):
        """Classe que cria e retornar uma conexão"""
        
        return pyodbc.connect(f'Driver={CONN[db][ENV]["driver"]};Server={CONN[db][ENV]["server"]};UID={CONN[db][ENV]["uid"]};PWD={CONN[db][ENV]["pwd"]}')

    def close(self, conn):
        conn.close()


