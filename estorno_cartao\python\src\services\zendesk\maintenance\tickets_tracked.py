from pathlib import Path
import sys
from typing import Dict
from datetime import datetime
from time import sleep
from urllib3.connectionpool import log as urlliblogger
import pandas as pd

def local_execution_with_others_projects_modules(current_file: str, directory_to_go_back: int = 1) -> Path:
    """
    If in a local execution the current module uses resources from another \n
    module that is in the same project i.e.(this module is in the same \n
    directory as the other module) and they aren't in the same level. \n
    So you need to add the common dictory between the modules to the Paths of execution \n
    
    Args:
        `current_file` (str): file path who calls the other modules

    Returns:
        `Path`: common directory Path
    """
    
    src_folder = Path(current_file.split(":")[0].upper() + ":" + current_file.split(":")[1])
    for _ in range(directory_to_go_back): src_folder = src_folder.parent
    if src_folder.__str__() not in sys.path: sys.path.append(str(src_folder))
    if Path.cwd().__str__() not in sys.path: sys.path.append(Path.cwd().__str__())

    return src_folder

if __name__ == "__main__":
    src_folder = local_execution_with_others_projects_modules(__file__, 4)

from services.logger import create_logger
from services.zendesk.api_requests import get_ticket_info_with_rate_limit,search_users_by_id, get_audits_for_ticket
from services.zendesk.zen_var import TICKET_SOLVED_STATUS
from Connections.search_db import SearchDb
from Connections.update_db import UpdateDb
from models.refund_log import estorno_venda_log_orm

log_level = 20
urlliblogger.setLevel(log_level)
if __name__ == '__main__': 
    ticket_fup_logger = create_logger('')
    ticket_fup_logger.setLevel(log_level)
else: 
    ticket_fup_logger = create_logger(__name__, without_handler = True)


MIGRATION_DATE = '08/10/2024 20:00:00'
MIGRATION_DATE_PATTERN = "%d/%m/%Y %H:%M:%S"

def verify_not_solved_tickets(env: str = "prd"):

    ticket_fup_logger.info("Iniciando verificação de ticket resolvidos")

    finder = SearchDb()
    changer = UpdateDb()

    refund_request_not_solved = []

    duality_stage_tickets, _ = finder.search_not_solved_ticket_in_duality_stage()
    after_migration_ticket, _ = finder.search_not_solved_after_migration(MIGRATION_DATE)

    if duality_stage_tickets is not None: refund_request_not_solved.extend(duality_stage_tickets)
    if after_migration_ticket is not None: refund_request_not_solved.extend(after_migration_ticket)
    

    if len(refund_request_not_solved) == 0:
        ticket_fup_logger.info("Sem ticket não resolvidos")
        return 0

    emails_ticket = []
    api_requests = 0
    for refund_request in refund_request_not_solved:

        
        api_requests = 1 + api_requests
        

        if refund_request["DATA_CADASTRO"] < datetime.strptime(MIGRATION_DATE, MIGRATION_DATE_PATTERN):

            ticket_res, response_headers = get_ticket_info_with_rate_limit(refund_request["NUMERO_DELIVERY"], env)
            ticket_id = refund_request["NUMERO_DELIVERY"]

        else:

            ticket_res, response_headers = get_ticket_info_with_rate_limit(refund_request["ID_INST_BPM"], env)
            ticket_id = refund_request["ID_INST_BPM"]
        
        if response_headers is None:
            ticket_fup_logger.error(f"Erro ao verificar o ticket {refund_request['NUMERO_DELIVERY']}")
            continue
        
        ratelimit = int(response_headers["ratelimit-limit"][1])
        ratelimit_remaining = int(response_headers["ratelimit-remaining"][1])

        if ratelimit == (0.5 * ratelimit_remaining): 
            sleep(62)


        if ticket_res.failure:
            raise Exception(ticket_res.error_description)

        if "error" in ticket_res.data:
            msg = "Error ao obter as informações do ticket"
            ticket_fup_logger.error(msg)
            raise Exception(msg)

        ticket_info: Dict = ticket_res.data

        status = ticket_info["ticket"]["status"] 
        if status != TICKET_SOLVED_STATUS:
            ticket_fup_logger.info("Ticket encerrado")
        
        user_id = ticket_res.data["ticket"]["requester_id"]

        user_info = search_users_by_id(user_id)
        if user_info.failure:
            ticket_fup_logger.error("Erro ao prococar pelo usuaário")
            continue

        email = user_info.data["user"]["email"]

        row = {"user_id":user_id, 
               "email": email, 
               "ticket_id": ticket_id, 
               "status": status, 
               "assunto_ticket": ticket_info["ticket"]["subject"]}
        
        emails_ticket.append(row)
        ticket_fup_logger.info(f"Ticket {ticket_id} adicionado")
        


    # Create a DataFrame from the list of dictionaries
    df = pd.DataFrame(emails_ticket)

    # Save the DataFrame to an Excel file
    output_file = 'email_solicitante.xlsx'
    df.to_excel(output_file, index=False)
    ticket_fup_logger.info("Finalizando verificação de ticket resolvidos")

    print("fim")
    

if __name__ == "__main__":

    # 366348 cartão prd
    # 366169 pix prd
    # solved 
    # new 
    prd_env = "prd" 
    homo_env = "homo"
    ticket_id_prd = 517471
    ticket_id_homo = 5195
    x = get_ticket_info_with_rate_limit(ticket_id_prd, env=prd_env)
    y = get_audits_for_ticket(ticket_id_prd)

    verify_not_solved_tickets(prd_env)

    print("fim")



