import requests
from requests.adapters import <PERSON><PERSON><PERSON><PERSON>pter
from requests.auth import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from datetime import datetime, timedelta, timezone
from urllib3.util.retry import Retry
from typing import Optional, Dict, Tuple
from utils import ResultHandle
from services.botcity_utils import get_maestro
from .credshop_vars import CREDS_CREDSHOP, TOKEN_REFUND_SCOPE, KEY_FOR_REFUND_ID
from services.logger import create_logger
from .credshop_utils import credshop_request_handler
import Config.vars as vars
from models.card_refund_info import CardRefundInfo
from models.refund_log import EstornoLog

logger = create_logger(__name__, without_handler=True)


class CredshopService:
    
    def __init__(self, env: str = "stg"):
        """Classe para lidar com a API da credshop

        Args:
            env (str, optional): opções de ambiente de uso da API. OPTIONS: "stg", "prd". Defaults to "stg".
        """
        self.access_token: Optional[str] = None
        self.env = env
        self.session = self._create_session()

    
    def process_credshop_refund(self, card_refund_info: CardRefundInfo, refund_info: EstornoLog) -> Tuple[ResultHandle, EstornoLog]:
        """Processar uma solicitação de estorno na credshop

        Args:
            card_refund_info (CardRefundInfo): Informações do cartão
            refund_info (EstornoLog): Informações do processamento de estorno

        Returns:
            Tuple[ResultHandle, EstornoLog]: Resultado do pedido de estorno, informações sobre processamento de estorno
        """

        refund_request_info = self._request_refund(card_refund_info)
        if refund_request_info.failure: return treat_refund_result(refund_request_info, refund_info)

        return treat_refund_result(refund_request_info, refund_info)
    
    
    @credshop_request_handler
    def track_refund(self, refund_id: int)-> ResultHandle:
        """Acompanhar um pedido de estorno na credshop

        Args:
            refund_id (int): Id do estorno na credshop

        Returns:
            ResultHandle: condição do estorno
        """

        if self._is_access_token_expired():
            if self._get_new_access_token().failure:
                return ResultHandle.Fail("Erro ao obter token de acesso")

        headers = {
            "Authorization": f"Bearer {self.access_token}",
            "Content-Type": "application/json"
        }

        response = self.session.get(
            url=CREDS_CREDSHOP[self.env]["track_refund_url"].format(refund_id = refund_id),
            headers=headers,
            timeout=(10, 30)
        )
        
        response.raise_for_status() 

        return response.json()
         

    def _create_session(self) -> requests.Session:
        """Criação de sessão de requisição com retry strategy

        Returns:
            requests.Session: sessão
        """
        session = requests.Session()
        
        retries = Retry(
            total=3,  
            backoff_factor=0.5,  
            status_forcelist=[500, 502, 503, 504],  
            allowed_methods=["POST", "GET"]  
        )
        
        adapter = HTTPAdapter(max_retries=retries)
        session.mount("http://", adapter)
        session.mount("https://", adapter)
        
        return session
    
    @credshop_request_handler
    def _get_new_access_token(self) -> ResultHandle:
        """Obter um novo token de acesso

        Raises:
            ValueError: Não encontrado o token de acesso na resposta

        Returns:
            ResultHandle: Erro na requisição de token de acesso
        """
        logger.debug("Iniciando a requisição de um novo token")

        headers = {
            'Content-Type': 'application/x-www-form-urlencoded',
            
        }
        payload = {
            "grant_type": "client_credentials",
            "scope": TOKEN_REFUND_SCOPE
        }

        cred = self._get_client_credentials()

        response = self.session.post(
            url=CREDS_CREDSHOP[self.env]["token_url"],
            data=payload,
            headers=headers,
            timeout=(10, 30),
            auth = HTTPBasicAuth(cred["client_id"], cred["client_secret"])
              
        )
        
        response.raise_for_status()  

        token_data: Dict= response.json()
        
        self._record_new_access_token(token_data)

        self.access_token = token_data.get('access_token')
        
        if not self.access_token:
            raise ValueError("Token de acesso não encontrado")
        
        logger.debug("Token de acesso obtido com sucesso")



    def _get_client_credentials(self) -> dict:
        """Obter credenciais de acesso Oauth 2.0 para credshop

        Raises:
            ValueError: credenciais não encontradas

        Returns:
            dict: credenciais de acesso
        """
        try:
            maestro = get_maestro()
            
            label = CREDS_CREDSHOP[self.env]["label"]
            client_id = maestro.get_credential(
                label=label, 
                key=CREDS_CREDSHOP[self.env]["keys_structure"]["client_id"]
            )
            client_secret = maestro.get_credential(
                label=label, 
                key=CREDS_CREDSHOP[self.env]["keys_structure"]["client_secret"]
            )

            if not client_id or not client_secret:
                raise ValueError("credenciais não encontradas")

            return {
                "client_id": client_id,
                "client_secret": client_secret 
            }
        
        except Exception as e:
            logger.error(f"Falha ao obter credenciais: {str(e)}")
            raise e
    
    def _record_new_access_token(self, token_data: dict):
        """Atualizar token de acesso expirado

        Args:
            token_data (dict): Informações do token de acesso atual
        """

        maestro = get_maestro()

        token_create_date = timestamp_with_timezone(token_data.get('created_at'))
        token_expiration_date = token_create_date + timedelta(seconds=token_data.get('expires_in'))   

        label = CREDS_CREDSHOP[self.env]["label"]

        maestro.update_credential(label=label, 
                                key="created_at", new_value= token_create_date.strftime(vars.ISO_DATE_FORMAT))
        
        maestro.update_credential(label=label, 
                                key="expires_in", new_value=token_expiration_date.strftime(vars.ISO_DATE_FORMAT))
        
        maestro.update_credential(label=label, 
                                key="access_token", new_value=token_data['access_token'])
            
        

    def _is_access_token_expired(self, hours_offset: int = -3) -> bool:
        """Verificar se o token de acesso expirou

        Args:
            hours_offset (int, optional): fuso horário. Defaults to -3.

        Returns:
            bool: condição de atual do token
        """
        try:

            maestro = get_maestro()
            label = CREDS_CREDSHOP[self.env]["label"]
            token_expiration_date = maestro.get_credential(label=label, key="expires_in")
            token_expiration_date = datetime.strptime(token_expiration_date, vars.ISO_DATE_FORMAT)

            if datetime.now(timezone(timedelta(hours=hours_offset))) > token_expiration_date:
                logger.info("Token de acesso expirado")
                return True
           
            self.access_token = maestro.get_credential(label=label, key="access_token")
            return False 
        
        except Exception as e:
            logger.error(f"Falha ao verificar se o token expirou: {str(e)}")
            return False 

        
    @credshop_request_handler
    def _request_refund(self, card_refund_info: CardRefundInfo)-> ResultHandle:
        """Solicitar estorno na credshop

        Args:
            card_refund_info (CardRefundInfo): Informações do cartão

        Returns:
            ResultHandle: Resultado do pedido de estorno
        """

        if self._is_access_token_expired():
            if self._get_new_access_token().failure:
                return ResultHandle.Fail("Erro ao obter token de acesso")

        headers = {
            "Authorization": f"Bearer {self.access_token}",
            "Content-Type": "application/json"
        }

        payload = {
            "stan": card_refund_info.nsu_tef,
            "sale_date": card_refund_info.sale_date.replace("-", ""),
            "authorization": card_refund_info.auth_code,
            "amount": card_refund_info.refund_value,
        } 

        response = self.session.post(
            url=CREDS_CREDSHOP[self.env]["refund_url"],
            json=payload,
            headers=headers,
            timeout=(10, 30)
        )
        
        response.raise_for_status() 

        return response.json()
    
    

def treat_refund_result(result: ResultHandle, 
            refund_info: EstornoLog) -> Tuple[ResultHandle, EstornoLog]:
        """Tratar resultado do estorno

        Args:
            result (ResultHandle): Resultado do pedido de estorno
            refund_info (EstornoLog): Infomações de estorno no grupo pmenos

        Returns:
            Tuple[ResultHandle, EstornoLog]:
        """

        if result.success:

            refund_info.status = vars.STATUS_WAIT_CREDSHOP
            refund_info.adquirente = vars.CREDSHOP_ACQUIRE_NAME
            refund_info.id_estorno_adquirente = result.data[KEY_FOR_REFUND_ID]
            return result, refund_info
        
        else:

            refund_info.status = vars.STATUS_PENDENCY_FOR_CSC
            refund_info.motivo = result.error_description
            refund_info.adquirente = vars.CREDSHOP_ACQUIRE_NAME
            return result, refund_info
        


def timestamp_with_timezone(timestamp, hours_offset=-3) -> datetime:
    """
    Converter timestamp para datetime com fuso horário especificado

    Args:
        timestamp (int): timestamp
        hours_offset (int, optional): fuso horário. Defaults to -3.

    Returns:
        datetime: datetime com fuso horário
    """

    tz = timezone(timedelta(hours=hours_offset))
    return datetime.fromtimestamp(timestamp, tz=tz)






