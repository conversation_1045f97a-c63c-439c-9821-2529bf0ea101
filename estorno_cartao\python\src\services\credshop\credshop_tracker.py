from services.logger import create_logger
from services.estorno_service import EstornoService
import Config.vars as global_vars
from Config.database import query_all_db
from models.refund_log import estorno_venda_log_orm
from utils import ResultHandle
from services.multi_report_handler import *
from models.refund_log import EstornoLog
from services.credshop.credshop_main import CredshopService
from typing import List
from utils import get_config
from datetime import datetime


credshop_tracker_logger = create_logger(__name__, without_handler = True)


class CredshopTracker():
  
    def __init__(self, env: str = "prd") -> None:
        self.credshop = CredshopService(env=env)
        self.estorno_service = EstornoService(get_config())

    def track_credshop_refunds(self) -> ResultHandle:
        """Consultar os pedidos de estorno na credshop
        """

        msg = f"Iniciando acampanhamento dos pedidos de estorno da {global_vars.CREDSHOP_ACQUIRE_NAME}"
        credshop_tracker_logger.info(msg)
        log_report(msg)

        credshop_refund_requests_result = self._get_credshop_refund_requests()

        if credshop_refund_requests_result.failure:
            credshop_tracker_logger.info(credshop_refund_requests_result.error_description)
            log_report(credshop_refund_requests_result.error_description)
            return -1
        
        if credshop_refund_requests_result.wait:
            credshop_tracker_logger.info(credshop_refund_requests_result.error_description)
            log_report(credshop_refund_requests_result.error_description)
            return 0
        
        credshop_refund_requests: List[EstornoLog] = credshop_refund_requests_result.data

        for refund_request in credshop_refund_requests:

            credshop_tracker_logger.info(f"Consultando pedido de estorno na credshop. id_estorno_fin: {refund_request.idEstorno}")
            try:
                refund_request_info = self.credshop.track_refund(refund_request.id_estorno_adquirente)
                if refund_request_info.failure:
                    msg = f"Erro ao consultar pedido de estorno na credshop. id_estorno_fin: {refund_request.idEstorno}"
                    credshop_tracker_logger.error(msg) 
                    log_report(msg)
                    continue

                if "code" not in refund_request_info.data:
                    msg = f"Caso de erro não mapeado nas consultas de estorno da credshop. id_estorno_fin: {refund_request.idEstorno}"
                    credshop_tracker_logger.info(msg)
                    log_report(msg) 
                    continue

                if refund_request_info.data["code"] != 1:
                    credshop_tracker_logger.info("Estorno em andamento")
                    continue

                credshop_tracker_logger.info("Estorno confirmado")
                refund_request.status = global_vars.STATUS_REFUND_PROCESS_END
                refund_request.motivo = global_vars.SUCCESSFUL_REFUND_REASON
                refund_request.data_resp_adquirente = datetime.now().strftime(global_vars.DATE_BASE_DATETIME_FORMAT)

                self.estorno_service.update_refund_processment(refund_request)

            except Exception as e:
                msg = f"Erro ao consultar pedido de estorno na credshop. id_estorno_fin: {refund_request.idEstorno}"
                credshop_tracker_logger.error(msg) 
                log_report(msg)
                credshop_tracker_logger.error(f"Detalhes do erro: {e.__traceback__}")
                continue
        

    def _get_credshop_refund_requests(self)-> ResultHandle:
        """Quais são os pedidos de estorno em andamento na Credshop

        Returns:
            ResultHandle: quando sucesso List[EstornoLog]
        """    

        TRACK_CREDSHOP_REFUND = f"""
                        SELECT *
                        FROM CosmosRPA.dbo.ESTORNO_VENDA_LOG ev_log (nolock)
                        WHERE 
                        ev_log.STATUS like '%{global_vars.CREDSHOP_ACQUIRE_NAME}%'
                        ORDER BY DATA_CADASTRO ASC
                        """
        
        try:
            refund_requests = query_all_db(TRACK_CREDSHOP_REFUND)
            result = list(map(estorno_venda_log_orm, refund_requests))

        except Exception as e:
            msg = f"Erro ao buscar estorno em andamento da {global_vars.CREDSHOP_ACQUIRE_NAME}"
            credshop_tracker_logger.error(msg)
            credshop_tracker_logger.error(f"Detalhes do erro: {e}")
            return ResultHandle.Fail(f"{msg} | {e}")

        if len(refund_requests) == 0:
            msg = f"Não foi encontrado nenhum estorno em andamento na {global_vars.CREDSHOP_ACQUIRE_NAME}"
            credshop_tracker_logger.info(msg)
            return ResultHandle.Waiting(error=msg)

        
        return ResultHandle.Ok(result)
    
    

