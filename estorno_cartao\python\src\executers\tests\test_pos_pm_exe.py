import sys
from pathlib import Path
from dataclasses import asdict


def local_execution_with_others_projects_modules(current_file: str, directory_to_go_back: int = 1) -> Path:
    """
    If in a local execution the current module uses resources from another \n
    module that is in the same project i.e.(this module is in the same \n
    directory as the other module) and they aren't in the same level. \n
    So you need to add the common dictory between the modules to the Paths of execution \n
    
    Args:
        `current_file` (str): file path who calls the other modules

    Returns:
        `Path`: common directory Path
    """
    
    src_folder = Path(current_file.split(":")[0].upper() + ":" + current_file.split(":")[1])
    for _ in range(directory_to_go_back): src_folder = src_folder.parent
    if src_folder.__str__() not in sys.path: sys.path.append(str(src_folder))
    if Path.cwd().__str__() not in sys.path: sys.path.append(Path.cwd().__str__())

    return src_folder

src_folder = local_execution_with_others_projects_modules(__file__, 3)

from executers.main_pos import POS
from services.telegram_bot_service import *
from services.logger import create_logger
import utils
from Connections.search_db import SearchDb
from models.refund_log import estorno_venda_log_orm, turn_estornolog_into_legacy_dict



#POINT OF SALE
################################
# Argumento 0: pos pmenos      #
# Argumento 1: tef pmenos      #
# Argumento 2: retorno Cielo   #
# Argumento 3: retorno Getnet  #
# Argumento 4: tef Extrafarma  #
# Argumento 5: pos Extrafarma  #
# Argumento 6: Atl.Token Cielo #
# Argumento 7: Retorno Nupay   #
# Argumento 8: Retorno Itau    #
# Argumento 9: Verificar tickets zendesk #
################################

main_logger = create_logger('', "logs.log")
main_logger.setLevel(20)

if __name__ == '__main__':  

    if len(sys.argv) > 1:
        option = int(sys.argv[1]) 
    else:
        option = 1

    app_config = utils.get_config()  

    # Multi-finalizador 
    # Uni-finalizador
    ids_estorno = (
445510,
445626,
442681,
442682,
442313,
442314,
445943,
445930,
445924,
445915,
445906,
445898,
445895,
445891,
445887,
445884,
445879,
445878,
445869,
445860,
445859,
445857,
445854,
445850,
445847,
445842,
445833,
445828,
445822,
445821,
445820,
445805,
445802,
445799,
445792,
445791,
445774,
445766,
445740,
445730,
445722,
445717,
445711,
445708,
445707,
445698,
445697,
445689,
445685,
445683,
445680,
445677,
445669,
445667,
445662,
445660,
445653,
445651,
445650,
445648,
445643,
445642,
445639,
445634,
445633,
445631,
445630,
445623,
445622,
445621,
445616,
445612,
445608,
445606,
445595,
445594,
445589,
445580,
445573,
445570,
445563,
445562,
445559,
445557,
445556,
445555,
445554,
445547,
445546,
445544,
445537,
445536,
445534,
445533,
445531,
445529,
445527,
445525,
445522,
445519,
445516,
445513,
445512,
445510,
445626,
445509,
445508,
445470,
445468,
445462,
445460,
445453,
445449,
445445,
445441,
445436,
445434,
445426,
445405,
445397,
445392,
445384,
445383,
445378,
445370,
445364,
445355,
445352,
445341,
445336,
445331,
445324,
445322,
445314,
445306,
445304,
445301,
445285,
445283,
445279,
445272,
445270,
445263,
445258,
445249,
445246,
445240,
445239,
445238) 

    requested_refund = []
    finder = SearchDb()

    for index, id_estorno in enumerate(ids_estorno):

        refund_info_raw = finder.search_refund_by_refund_id_in_estorno_venda(id_estorno)
        refund_info = estorno_venda_log_orm(refund_info_raw[0][0])

        refund_info.idDevtrocaCab = int(refund_info.idDevtrocaCab)
        refund_info_legacy = turn_estornolog_into_legacy_dict(refund_info)

        requested_refund.append(refund_info_legacy)

    try:
        POS(app_config, option).execute_process(requested_refund)
    

    # Tratando exessões do processo
    except Exception as e:

        if option == 0:
            msg = 'ESTORNO VENDA - ERRO POS:\n{}'.format('error {} - desc: {}'.format(e.__class__, e))

            if (msg != "ESTORNO VENDA - ERRO POS:\nerror <class 'ValueError'> - desc: Erro na requisicao: status 404"):

                telegram_bot_sendtext(msg)
        
        if option == 1:
            telegram_bot_sendtext('ESTORNO VENDA - ERRO RETORNO TEF:\n{}'.format('error {} - desc: {}'.format(e.__class__, e)))
        
        if option == 2:
            telegram_bot_sendtext('ESTORNO VENDA - ERRO RETORNO:\n{}'.format('error {} - desc: {}'.format(e.__class__, e)))
        
        if option == 3:
            telegram_bot_sendtext('ESTORNO VENDA - ERRO RETORNO GET:\n{}'.format('error {} - desc: {}'.format(e.__class__, e)))
        
        if option == 4:
            telegram_bot_sendtext('ESTORNO VENDA - ERRO RETORNO TEF extrafarma:\n{}'.format('error {} - desc: {}'.format(e.__class__, e)))
        
        if option == 5:
            telegram_bot_sendtext('ESTORNO VENDA - ERRO RETORNO POS extrafarma:\n{}'.format('error {} - desc: {}'.format(e.__class__, e)))
        
        if option == 6:
            telegram_bot_sendtext('ESTORNO VENDA - ERRO AO ATUALIZAR TOKEN:\n{}'.format('error {} - desc: {}'.format(e.__class__, e)))
        
        if option == 7:
            telegram_bot_sendtext('ESTORNO VENDA - ERRO RETORNO NUPAY:\n{}'.format('error {} - desc: {}'.format(e.__class__, e)))
           
        if option == 8:
            telegram_bot_sendtext('ESTORNO VENDA - ERRO RETORNO ITAU:\n{}'.format('error {} - desc: {}'.format(e.__class__, e)))
            
        if option != 1:
            msg = 'ESTORNO VENDA - ERRO TEF:\n{}'.format('error {} - desc: {}'.format(e.__class__, e))

            if (msg != "ESTORNO VENDA - ERRO TEF:\nerror <class 'ValueError'> - desc: Erro na requisicao: status 404"):

                telegram_bot_sendtext(msg)
            

         
        raise e   
    