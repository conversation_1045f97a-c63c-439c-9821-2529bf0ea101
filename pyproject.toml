[project]
name = "rpa-estornov3-pdi"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.8"
dependencies = [
    "beautifulsoup4==4.11.1",
    "botcity-framework-base==0.3.1",
    "botcity-framework-core==0.3.0",
    "botcity-framework-web==0.6.0",
    "botcity-maestro-sdk==0.6.0",
    "openpyxl==3.0.9",
    "pandas==1.3.4",
    "psycopg2==2.9.3",
    "pydantic==2.9.2",
    "pyodbc==4.0.32",
    "pytest==8.3.5",
    "pytest-mock>=3.14.1",
    "requests==2.32.4",
    "requests-oauthlib==1.3.1",
    "urllib3==1.26.8",
    "xlrd==2.0.1",
]

[tool.pytest.ini_options]
addopts = "-v"
pythonpath = ["estorno_cartao", "estorno_cartao/python/src", "estorno_cartao/python"]
testpaths = ["estorno_cartao/python/tests"]

