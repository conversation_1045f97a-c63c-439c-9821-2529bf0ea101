import requests
from xml.etree import Element<PERSON>ree
from typing import Union, Dict
from datetime import datetime
from pathlib import Path
import sys
from dataclasses import dataclass


def local_execution_with_others_projects_modules(current_file: str, directory_to_go_back: int = 1) -> Path:
    """
    If in a local execution the current module uses resources from another \n
    module that is in the same project i.e.(this module is in the same \n
    directory as the other module) and they aren't in the same level. \n
    So you need to add the common dictory between the modules to the Paths of execution \n
    
    Args:
        `current_file` (str): file path who calls the other modules

    Returns:
        `Path`: common directory Path
    """
    
    src_folder = Path(current_file.split(":")[0].upper() + ":" + current_file.split(":")[1])
    for _ in range(directory_to_go_back): src_folder = src_folder.parent
    if src_folder.__str__() not in sys.path: sys.path.append(str(src_folder))
    if Path.cwd().__str__() not in sys.path: sys.path.append(Path.cwd().__str__())

    return src_folder

src_folder = local_execution_with_others_projects_modules(__file__, 2)


from utils import XML_MSG_GETNET_POST_CANCELAMENTO, XML_MSG_GETNET_RECEIVE
from Config.vars import EXTRA_FARMA_CASE, PAGUE_MENOS_CASE
from services.multi_report_handler import *
from models.card_refund_info import CardRefundInfo


@dataclass
class QueryCancelTransactionRequest:
    branch: str
    terminal: str
    date: str
    nsu: str



class ServiceGetnet():

    def __init__(self, config:dict):
        self.config = config
        self.__api_base_url = config['api']['getnet']['api_base_url']
        self.__username = config['api']['getnet']['username']
        self.__password = config['api']['getnet']['password']
        self.__username_ext = config['api']['getnet']['username_ext']
        self.__password_ext = config['api']['getnet']['password_ext']
        super().__init__()
            
    def verify_cancelation_request_by_sale_info(self, sale_info: QueryCancelTransactionRequest, venda: Dict):

        url = self.__api_base_url
        if venda['nomeEmpresa'] == "Pague Menos" or venda['nomeEmpresa'] == 'PAGUE_MENOS':
            username=self.__username
            password=self.__password
        
        elif venda['nomeEmpresa'] == "Extrafarma":
            username = self.__username_ext
            password =self.__password_ext
       
        
        headers = {'content-type': 'text/xml'}

        body = fill_search_refunf_xml_by_sale_info(sale_info, username, password)
        response = requests.post(url,data=body, headers=headers)
        root = ElementTree.fromstring(response.text.encode('utf-8'))

        namespaces = {
        'S': "http://schemas.xmlsoap.org/soap/envelope/",
        'ns0': "http://br.com.getnet.reqcanc.ws.service/"
        }

        # Find the 'protocol' element using XPath
        protocol_element = root.find('.//ns0:queryCancelTransationByKeyResponse//extra/protocol', namespaces=namespaces)
        
        if protocol_element is None:
            venda["protocoloGetnet"] = protocol_element
        else:
            venda["protocoloGetnet"] = protocol_element.text.strip()
        
        return venda

        



    def request_getnet_service(self, protocolo,empresa):
        url = self.__api_base_url
        if empresa == "Pague Menos" or empresa == 'PAGUE_MENOS':
            username=self.__username
            password=self.__password
        
        elif empresa == "Extrafarma":
            username = self.__username_ext
            password =self.__password_ext
       
        
        headers = {'content-type': 'text/xml'}

        body = XML_MSG_GETNET_RECEIVE.format(username, password, protocolo)
        print(body)
        response = requests.post(url,data=body, headers=headers)
        print(response.text)
        tree = ElementTree.fromstring(response.text.encode('utf-8'))

        print(response.text)

        status_message = tree.find(".//processingResultMessage").text
        result_code = tree.find(".//processingResultCode").text
        status_message = status_message.strip()
        result_code = result_code.strip()
        return status_message, result_code

    def cancel_getnet_service(self, 
                              card_requesition_info: CardRefundInfo, 
                              empresa:str):
        
        url = self.__api_base_url
        
        if empresa == PAGUE_MENOS_CASE:
            username=self.__username
            password=self.__password
        
        elif empresa == EXTRA_FARMA_CASE:
            username = self.__username_ext
            password =self.__password_ext
            

        print("BRANCH = " + card_requesition_info.establishment_code)

        dataVenda = card_requesition_info.sale_date
        modality = card_requesition_info.installments
        autorization = card_requesition_info.auth_code
        inst_num = card_requesition_info.installments

        dataVenda = self.__deal_date_venc(dataVenda)
        modality = self.__veific_modality(modality)
        
        headers = {'content-type': 'text/xml'}
        body = XML_MSG_GETNET_POST_CANCELAMENTO.format(
            username,
            password,
            card_requesition_info.establishment_code,
            card_requesition_info.terminal,
            autorization,
            dataVenda,
            modality,
            card_requesition_info.payment_method_value, 
            inst_num, 
            card_requesition_info.nsu_external,
            card_requesition_info.refund_value
        )

        print('body')
        print(body)
           
        response = requests.post(url,data=body,headers=headers)

        if response.status_code == 403:
            msg = "Acesso negado a API da getnet"
            print(msg)
            log_report(msg)
            return -1

        print(response.content)

        print('Codigo de retorno API Getnet {}'.format(response.status_code))
        try:
            tree = ElementTree.fromstring(response.text.encode('utf-8'))
            numero_protocolo = tree.find(".//protocol").text
            print('Numero do protocolo {}'.format(numero_protocolo))
            return numero_protocolo
        except:
            print('error')
            return -1


    
    def __deal_date_venc(self, dataVenda: str):
        
        if "/" in dataVenda:
            new_dataVenda = dataVenda.replace("/", "")

        elif "-" in dataVenda:
            new_dataVenda = datetime.strptime(dataVenda,"%Y-%m-%d").strftime("%d%m%Y")
        
        else:
            print(f"Tipo de data não mapeado: {dataVenda}")
        
        return new_dataVenda

    def __veific_modality(self, modality):
        if modality > 1:
            modality = 'P'
            return modality
        else:
            modality = 'V'
            return modality



def fill_search_refunf_xml_by_sale_info(request: QueryCancelTransactionRequest, user: str, password: str) -> str:
    xml_template = """<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:br="http://br.com.getnet.reqcanc.ws.service/">
<soapenv:Header/>
<soapenv:Body>
    <br:queryCancelTransationByKey>
        <arg0>
            <authentication>
                <username>{user}</username>
                <password>{password}</password>
            </authentication>
            <queryByKey>
                <branch>{branch}</branch>
                <terminal>{terminal}</terminal>
                <date>{date}</date>
                <nsu>{nsu}</nsu>
            </queryByKey>
        </arg0>
    </br:queryCancelTransationByKey>
</soapenv:Body>
</soapenv:Envelope>"""
    
    return xml_template.format(
        user = user,
        password = password,
        branch=request.branch,
        terminal=request.terminal,
        date=request.date,
        nsu=request.nsu
    )

