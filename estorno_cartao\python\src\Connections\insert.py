import Connections.scripts as scripts
from Connections.dao import DAO
from typing import Dict
from datetime import datetime

class InsertMaster:

    def __init__(self):
        self.dao = DAO()


    def insert_refund_processment(self, values_dict: Dict):

        nome_do_servidor = "cosmos"
        conn = self.dao.create_connection(db=nome_do_servidor)
        cursor = conn.cursor()

        datatime_columns = ["DATA_CADASTRO", "DATA_ALTERACAO"]
        columns_list = list(values_dict.keys())
        columns_list.extend(datatime_columns)

        columns = ', '.join(columns_list)
        placeholders = ', '.join(['?'] * len(columns_list))

        query = f"""
        INSERT INTO COSMOSRPA.DBO.ESTORNO_VENDA_LOG 
        ({columns})
        VALUES ({placeholders})
        """

        values_dict["DATA_MOVIMENTO"] = datetime.strptime(values_dict["DATA_MOVIMENTO"], "%Y-%m-%d")

        if '.' in values_dict["DATA_DEVOLUCAO"]:
            date_string = values_dict["DATA_DEVOLUCAO"][:values_dict["DATA_DEVOLUCAO"].index('.')] 
            date_obj = datetime.strptime(date_string,"%Y-%m-%dT%H:%M:%S")
        else:
            #Criação de ticket manual pela estorno venda 
            if "T" not in values_dict["DATA_DEVOLUCAO"]:
                date_obj = datetime.strptime(values_dict["DATA_DEVOLUCAO"],"%Y-%m-%d")
            else:
                date_obj = datetime.strptime(values_dict["DATA_DEVOLUCAO"],"%Y-%m-%dT%H:%M:%S")

            

        values_dict["DATA_DEVOLUCAO"] = date_obj

        values_dict["DATA_VENDA"] = datetime.strptime(values_dict["DATA_VENDA"], "%Y-%m-%d")

        values = list(values_dict.values())
        values.extend(["x", "y"])


        values[-2] = datetime.today()
        values[-1] = datetime.today()

        try:
           
            cursor.execute(query, values)
            cursor.commit()
            
            return None, 0

        except Exception as e:
            print(f"Não foi possível inserir. Detalhe: {e}")
            raise e
        finally:
            self.dao.close(conn)