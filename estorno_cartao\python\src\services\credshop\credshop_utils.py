from functools import wraps
from typing import Callable, Any, Optional, TypeVar
from utils import ResultHandle
from requests.exceptions import ConnectionError, Timeout, RequestException
from .credshop_vars import NOT_A_MAPPED_ERROR_MSG
import logging
from services.logger import create_logger

credshop_utils_logger = create_logger(__name__, without_handler=True)

T = TypeVar('T', bound=Callable[..., Any])

def credshop_request_handler(_func: T = None, *, 
                         logmsg: Optional[str] = None,
                         module_name: Optional[str] = None) -> T:
    """
    Decorator to handle API request exceptions with standardized logging and error handling.
    
    Args:
        _func: Function to decorate (used when decorator is called without arguments)
        return_result_handle: If True, wrap the result in a ResultHandle object
        logmsg: Custom log message
        module_name: Custom module name for logger
        
    Returns:
        Decorated function
    """
    def decorate_with_handler(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, **kwargs) -> ResultHandle:

            module = module_name if module_name is not None else func.__module__
            logger = logging.getLogger(module)
            
            msg = logmsg if logmsg is not None else f"Erro na funcão {func.__name__} da credishop"
            
            try:
                result = func(*args, **kwargs)
                if isinstance(result, ResultHandle): return result
                return ResultHandle.Ok(result)
                
            except ConnectionError as e:
                logger.error(f"{msg}: Connection error - {str(e)}")
                return get_credshop_erros_msg(e)
                
            except Timeout as e:
                logger.error(f"{msg}: Request timed out - {str(e)}")
                return get_credshop_erros_msg(e)
                
            except RequestException as e:
                logger.error(f"{msg}: Request failed - {str(e)}")
                return get_credshop_erros_msg(e)

            except Exception as e:
                logger.error(f"{msg}: {str(e)}")
                return ResultHandle.Fail(str(e), data=NOT_A_MAPPED_ERROR_MSG)

        return wrapper

    if _func is None:
        return decorate_with_handler
    else:
        return decorate_with_handler(_func)


def get_credshop_erros_msg(exception: RequestException) -> ResultHandle:
    """Extrair mensagem de erro da credshop

    Args:
        exception (RequestException): erro de requisição

    Returns:
        ResultHandle: resultado do erro
    """
    try:
        response = exception.response.json()

        if "errors" in response:
            error_msg = shrink_message(response["errors"][0])
        elif "error" in response: 
            error_msg = shrink_message(response["error"])
        elif ("errors" not in response) and ("error" not in response):
            error_msg = shrink_message(response["message"])
        else:
            error_msg = NOT_A_MAPPED_ERROR_MSG
            
        credshop_utils_logger.info(f"Erro informado pela credshop: {error_msg}")
    
    except Exception as e:
        credshop_utils_logger.info(f"Erro ao capturar a mensagem de erro da credshop: {str(e)}")
        if exception.response.status_code == 404:
            error_msg = "Requisição de estorno não encontrada"
        else:
            error_msg = NOT_A_MAPPED_ERROR_MSG

    return ResultHandle.Fail(error_msg, data=exception.response)



def shrink_message(msg: str, max_lenght_of_chars: int = 254) -> str:
    """Cortar a messagem de modo a não dar erro de overflow no banco

    Args:
        msg (str): String da menssagem 
        max_lenght_of_chars (int, optional): Tamanho máximo da string

    Returns:
        str: Mensagem cortada
    """

    if msg is not None and len(msg) > max_lenght_of_chars:
        msg = msg[:254]
        return msg
    else:
        return msg