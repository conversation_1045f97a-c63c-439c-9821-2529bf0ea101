from dataclasses import dataclass
from typing import Dict




@dataclass
class BranchIdentificationInAcquirer:
    terminal: str
    establishment: str
    branch: int


def load_orm_branch_id_in_cielo_tef(data: Dict) -> BranchIdentificationInAcquirer:

    branch_in_acquirer = BranchIdentificationInAcquirer(
        terminal = data['num_terminal'],
        establishment = data['cod_estabelecimento'][:-4].lstrip("0"),
        branch = int(data['cod_filial']))

    return branch_in_acquirer


def load_orm_branch_id_in_getnet_tef(data: Dict) -> BranchIdentificationInAcquirer:

    branch_in_acquirer = BranchIdentificationInAcquirer(
        terminal = data['num_terminal'],
        establishment = data['cod_estabelecimento'].lstrip("0"),
        branch = int(data['cod_filial']))

    return branch_in_acquirer