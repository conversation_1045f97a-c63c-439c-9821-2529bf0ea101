from requests import get, post
import sys
from pathlib import Path
from typing import Dict, <PERSON><PERSON>




def local_execution_with_others_projects_modules(current_file: str, directory_to_go_back: int = 1) -> Path:
    """
    If in a local execution the current module uses resources from another \n
    module that is in the same project i.e.(this module is in the same \n
    directory as the other module) and they aren't in the same level. \n
    So you need to add the common dictory between the modules to the Paths of execution \n
    
    Args:
        `current_file` (str): file path who calls the other modules

    Returns:
        `Path`: common directory Path
    """
    
    src_folder = Path(current_file.split(":")[0].upper() + ":" + current_file.split(":")[1])
    for _ in range(directory_to_go_back): src_folder = src_folder.parent
    if src_folder.__str__() not in sys.path: sys.path.append(str(src_folder))
    if Path.cwd().__str__() not in sys.path: sys.path.append(Path.cwd().__str__())

    return src_folder

if __name__ == "__main__":
    src_folder = local_execution_with_others_projects_modules(__file__, 3)

import Config.vars as vars
from utils import get_config, ResultHandle
import services.nupay.nupay_var as nu_var
import services.nupay.nu_tools as nu_tools
from Config.vars import HYBRID_CASE_CREDIT, HYBRID_CASE_DEBIT


class ServiceNuPay():
    
    def __init__(self, config: dict, entreprise: str, env: str = "prd_env"): 
        """Serviço de estorno para Nupay

        Args:
            `config` (dict): Parâmetro de configuração da API \n

            `entreprise` (str): Código da empresa, e.g.:
                                pm -> pagemenos \n
                                exf -> extrafarma \n

            `env` (str, optional): Ambiente do Nupay que será usado. \n
                                test_env -> ambiente de teste \n
                                prd_env -> ambiente de produção \n
        """
        self.config = config
        self.env = env
        self.entreprise = entreprise

    def refund_sale(self, tid: str, refund_info: Dict)-> Tuple[ResultHandle, Dict]:
        """Estorno venda feita no nupay

        Args:
            tid (str): Id da transação
            refund_info (Dict): Infomações de estorno no grupo pmenos

        Returns:
            Tuple[ResultHandle, Dict]
        """
        
        sale_info = self.search_sale(tid)
        
        if sale_info.failure: return self._treat_refund_result(sale_info, refund_info)
        sale_verification, refund_info = self._can_sale_be_refunded(sale_info.data, refund_info)
        if sale_verification.failure: return self._treat_refund_result(sale_verification, refund_info)
        

        payload = self._make_refund_payload(refund_info)
        headers = nu_tools.make_headers(self.config, self.env, self.entreprise)
        request_refund_url: str = self.config['nupay'][self.env]['request_refund_url']
        
        try: 
            api_res = post(url= request_refund_url.format(tid), headers= headers, json=payload)
        except Exception as error:
            msg = "Erro ao pedir estorno na API"
            print(f"Detalhe do erro: {error}")
            return self._treat_refund_result(ResultHandle.Fail(msg), refund_info)
        
        request_verification = nu_tools.validate_request(api_res)
        if request_verification.failure:
            return self._treat_refund_result(request_verification, refund_info)
        
        refund_request = api_res.json()

        refund_verification = self._validate_refund(refund_request)
        if refund_verification.failure:
            return self._treat_refund_result(refund_verification, refund_info)
        
        return self._treat_refund_result(ResultHandle.Ok(refund_request), refund_info)
    
    def _treat_refund_result(self, result: ResultHandle, 
            refund_info: Dict) -> Tuple[ResultHandle, Dict]:
        """Tratar resultado do estorno

        Args:
            result (ResultHandle): Resultado do pedido de estorno
            refund_info (Dict): Infomações de estorno no grupo pmenos

        Returns:
            Tuple[ResultHandle, Dict]:
        """

        if result.success:
            refund_info["status"] = vars.STATUS_REFUND_PROCESS_END
            refund_info["motivo"] = vars.SUCCESSFUL_REFUND_REASON
            refund_info["id_estorno_adquirente"] = result.data["refundId"]
            return result, refund_info
        
        if not isinstance(result.data, dict):
            refund_info["status"] = vars.STATUS_PENDENCY_FOR_CSC
            refund_info["motivo"] = result.error_description
            return result, refund_info
        
        if result.error_description == nu_var.SALE_FULLY_REFUNDED_MSG:
            refund_info["status"] = vars.STATUS_REFUND_PROCESS_END
            refund_info["motivo"] = result.error_description

            sale_info = self.search_sale(refund_info["tid"])
            refund_info["id_estorno_adquirente"] = sale_info.data["refunds"][0]["refundId"]
            return result, refund_info
        
        if result.wait:
            refund_info["status"] = vars.STATUS_WAIT_NUPAY
            refund_info["motivo"] = None
        else:
            refund_info["status"] = vars.STATUS_PENDENCY_FOR_CSC
            refund_info["motivo"] = result.error_description
        
        if "refundId" in tuple(result.data.keys()):
                refund_info["id_estorno_adquirente"] = result.data["refundId"]
        
        return result, refund_info


    def search_sale(self, tid: str) -> ResultHandle:
        """Procuar pela venda na Nubank

        Args:
            tid (str): Id da transação

        Returns:
            ResultHandle: Dict
        """

        search_sale_url: str = self.config['nupay'][self.env]['search_sale_url']
        headers = nu_tools.make_headers(self.config, self.env, self.entreprise)

        try: 
            api_res = get(url= search_sale_url.format(tid), headers= headers)
        except Exception as error:
            msg = "Erro ao consultar venda na API"
            print(f"Detalhe do erro: {error}")
            return ResultHandle.Fail(msg)
        
        verifier_result = nu_tools.validate_request(api_res)
        if verifier_result.failure:
            return verifier_result
            
        api_res_dict = api_res.json()

        return ResultHandle.Ok(api_res_dict)
    
    def _can_sale_be_refunded(self, sale_info: Dict, refund_info: Dict)-> Tuple[ResultHandle, Dict]:
        """Verificar se venda foi concluída corretamente

        Args:
            sale_info (Dict): Infomações de estorno na Nubank
            refund_info (Dict): Infomações de estorno no grupo pmenos

        Returns:
            Tuple[ResultHandle, Dict]
        """

        if sale_info["status"] in tuple(nu_var.sale_abnormal_status_and_standard_msg.keys()):
            msg = nu_var.sale_abnormal_status_and_standard_msg[sale_info["status"]]
            return ResultHandle.Fail(msg, sale_info), refund_info
        
        if sale_info["status"] == nu_var.NUPAY_WAIT:
            return ResultHandle.Waiting(vars.STATUS_WAIT_NUPAY, sale_info), refund_info
        
        if sale_info["status"] != nu_var.NUPAY_COMPLETED:
            return ResultHandle.Fail("Status de venda não mapeado", sale_info), refund_info

        refund_info["bandeira"] = refund_info["bandeira"] + self._get_payment_type(sale_info)
        return ResultHandle.Ok(sale_info), refund_info
    
    
    def _make_refund_payload(self, refund_info: Dict)-> Dict:
        """Criar payload para estorno

        Args:
            refund_info (Dict): Infomações de estorno no grupo pmenos

        Returns:
            Dict: payload construido
        """

        payload = {
        "transactionRefundId": str(refund_info["idEstorno"]),
        "amount": {
            "value": refund_info["valorEstorno"],
            "currency": "BRL"
        },
        "notes": f"Pedido de devolução {refund_info['idDevtrocaCab']}"
        }

        return payload
    
    def _validate_refund(self, refund_request: Dict) -> ResultHandle:
        """Validar pedido de estorno

        Args:
            refund_request (Dict): Informações do pedido de estorno

        Returns:
           Tuple[ResultHandle, Dict]
        """
        
        if refund_request["status"] == nu_var.NUPAY_ERROR:
            msg = "Erro no pedido de estorno"
            return ResultHandle.Fail(msg, refund_request)
        
        if refund_request["status"] == nu_var.NUPAY_WAIT_REFUND:
            return ResultHandle.Waiting(vars.STATUS_WAIT_NUPAY, refund_request)
        
        if refund_request["status"] != nu_var.NUPAY_REFUNDED:
            return ResultHandle.Fail("Status de estorno não mapeado", refund_request)

        return ResultHandle.Ok(refund_request)
    
    def _get_payment_type(self, nupay_sale_info: Dict) -> str:
        """Identificar qual é o tipo de pagamento para ser indicado
        no chamado

        Args:
            nupay_sale_info (Dict): Informações da compra no Nubank

        Returns:
            str: Texto inficando qual é tipo de pagamento
        """

        if "paymentType" not in tuple(nupay_sale_info.keys()):
            return ''
        if nupay_sale_info['paymentType'] == 'credit':
            return HYBRID_CASE_CREDIT
        else:
            return HYBRID_CASE_DEBIT
        

if __name__ == '__main__':

    refund_info = {
        "idEstorno": "dsgsdg684",
        "idDevtrocaCab": "********",
        "valorEstorno": 1,
        "status": None,
        "cod_autori": None}

    app_config = get_config()  
    nupay = ServiceNuPay(app_config, vars.PAGUE_MENOS_CASE)

    sale_info = nupay.search_sale("********-7590-4b5c-ab96-3a721feb0bd8")

    refund_request, refund_info = nupay.refund_sale("c36fa8fe-d030-4f6f-931b-33c185e6831c", refund_info)

    print("fim")

