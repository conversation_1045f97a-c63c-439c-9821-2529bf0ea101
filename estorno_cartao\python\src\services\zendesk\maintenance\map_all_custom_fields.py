from pathlib import Path
import sys
from typing import Dict

def local_execution_with_others_projects_modules(current_file: str, directory_to_go_back: int = 1) -> Path:
    """
    If in a local execution the current module uses resources from another \n
    module that is in the same project i.e.(this module is in the same \n
    directory as the other module) and they aren't in the same level. \n
    So you need to add the common dictory between the modules to the Paths of execution \n
    
    Args:
        `current_file` (str): file path who calls the other modules

    Returns:
        `Path`: common directory Path
    """
    
    src_folder = Path(current_file.split(":")[0].upper() + ":" + current_file.split(":")[1])
    for _ in range(directory_to_go_back): src_folder = src_folder.parent
    if src_folder.__str__() not in sys.path: sys.path.append(str(src_folder))
    if Path.cwd().__str__() not in sys.path: sys.path.append(Path.cwd().__str__())

    return src_folder

if __name__ == "__main__":
    src_folder = local_execution_with_others_projects_modules(__file__, 4)

from services.logger import create_logger
import csv
from services.zendesk.api_requests import get_ticket_info, get_field_info


if __name__ == '__main__': 
    zen_mapper_loger = create_logger('')
    zen_mapper_loger.setLevel(10)
else: 
    zen_mapper_loger = create_logger(__name__, without_handler = True)


def main(ticket_id: int, file_name: str, env: str = "prd"):

    ticket_info = get_ticket_info(ticket_id, env)
    custom_fields = ticket_info.data["ticket"]["custom_fields"]


    form_fields_descriptions = []
    for field_id_and_value in custom_fields:

        if field_id_and_value["value"] is None: continue

        field_info_referee = get_field_info(field_id_and_value["id"], env)
        if field_info_referee.failure:
            zen_mapper_loger.error("Finalizando aplicação devido a erro")
            exit()
        
        field_info: Dict = field_info_referee.data["ticket_field"]

        print(f"Id: {field_info['id']}; name: {field_info['title']}")

        field_description_item = {
        "id": field_info["id"] ,
        "title": field_info["title"],
        "value": field_id_and_value["value"],
        "tag": field_info["tag"]
        }

        if "custom_field_options" in tuple(field_info.keys()):
            field_description_item["with_options"] = True
        else:
            field_description_item["with_options"] = False


        form_fields_descriptions.append(field_description_item)

    folder = Path(__file__).parent
    with open(folder / file_name, "w", newline= "\n", encoding= "utf-8") as f_io:

        csv_writer = csv.DictWriter(f_io, fieldnames = field_description_item.keys())
        csv_writer.writeheader()
        csv_writer.writerows(form_fields_descriptions)


if __name__ == "__main__":

    # 366348 cartão prd
    # 366169 pix prd
    # env_options: "prd", "homo"

    
    env = "prd" 
    ticket_id = 2029183
    file_name = f"custom_fields_{ticket_id}_env_{env}.csv"
    
    field_info_referee = get_field_info(26546968004109, env)

    main(ticket_id, file_name, env)

    print("fim")



