
request_error_and_standard_msg = {
    400: None,
    401:"Acesso não autorizado a API",
    404: None,
    412: None,
    422: None,
    429: "Algum limite de uso da API foi atingido"
}

REMAINING_PERCENTAGE_TO_BE_ABOVE_OF = 0.75

TICKET_INFO_END_PONT = 'https://{subdomain}/api/v2/tickets/{ticket_id}'
TICKET_INFO_END_PONT = 'https://{subdomain}/api/v2/tickets/{ticket_id}'
TICKET_AUDITS_END_PONT = 'https://{subdomain}/api/v2/tickets/{ticket_id}/audits?page[size]=100'
FIELD_INFO_END_POINT = 'https://{subdomain}/api/v2/ticket_fields/{field_id}'
CREATE_TICKET_END_POINT = 'https://{subdomain}/api/v2/tickets'
SEARCH_USER_END_POINT = 'https://{subdomain}/api/v2/users/search'
SEARCH_USER_END_POINT_BY_ID = "https://{subdomain}/api/v2/users/{user_id}"


TICKET_SOLVED_STATUS = "solved"
TICKET_CLOSED_STATUS = "closed"

ENDING_STATUS = ("solved", "closed")
PENDING = 'pending'
NEW = 'new'

STANDARD_TICKET_COMMENT_PENDENCY = """
    <p>Houve um problema no atendimento automatizado de estorno. Ele foi direcionado para o setor de CSC</p>
    <p>Detalhes do estorno:</p>
    <p>Venda do tipo {refund_info.bandeira} com finalizador {refund_info.finalizador}</p>

    <p><strong>Motivo: </strong>{refund_info.motivo}</p>

    <p><strong>Nome do cliente: </strong>{refund_info.nomeCliente}</p>

    <p>
        <strong>Filial da venda: </strong> {refund_info.codigoFilialOrigem};
        <strong>CNPJ filial da venda: </strong> {refund_info.cnpjFilialOrigem};
        <strong>Filial do pedido de estorno: </strong> {refund_info.codigoFilialDestino}; 
    </p>

    <p>
        <strong>NSU TEF: </strong> {refund_info.nsutef}; 
        <strong>NSU HOST: </strong> {refund_info.nsuhost};
        <strong>TID: </strong> {refund_info.tid};
    </p>

    <p>
        <strong>Nº cupom: </strong> {refund_info.numeroCupom}; 
        <strong>COO: </strong> {refund_info.numeroCoo}; 
        <strong>Número pedido E-commerce: </strong> {refund_info.numeroPedidoVTEX}; 
    </p>

    <p>
        <strong>Telefone: </strong> {refund_info.telefone}; 
        <strong>e-mail: </strong> {refund_info.email}; 
        <strong>cartão sempre: </strong> {refund_info.cartaoSempre}
    </p>

    <p>
        <strong>Nº troca-devolução: </strong> {refund_info.idDevtrocaCab}; 
        <strong>ID estorno: </strong> {refund_info.idEstorno};
        <strong>Nº pré-venda: </strong> {refund_info.numeroPreVenda};
    </p>

    <p>
        <strong>Código de autorização: </strong> {refund_info.codigoAutorizacao}; 
        <strong>Dígitos do cartão: </strong> {refund_info.n_cartao};
    </p>
    """

