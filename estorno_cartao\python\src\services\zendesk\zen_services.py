
import sys
from pathlib import Path
from json import loads
from typing import Dict, <PERSON>, List


def local_execution_with_others_projects_modules(current_file: str, directory_to_go_back: int = 1) -> Path:
    """
    If in a local execution the current module uses resources from another \n
    module that is in the same project i.e.(this module is in the same \n
    directory as the other module) and they aren't in the same level. \n
    So you need to add the common dictory between the modules to the Paths of execution \n
    
    Args:
        `current_file` (str): file path who calls the other modules

    Returns:
        `Path`: common directory Path
    """
    
    src_folder = Path(current_file.split(":")[0].upper() + ":" + current_file.split(":")[1])
    for _ in range(directory_to_go_back): src_folder = src_folder.parent
    if src_folder.__str__() not in sys.path: sys.path.append(str(src_folder))
    if Path.cwd().__str__() not in sys.path: sys.path.append(Path.cwd().__str__())

    return src_folder

if __name__ == "__main__":
    src_folder = local_execution_with_others_projects_modules(__file__, 3)

from services.zendesk.api_requests import create_ticket, search_users_by_pattern, search_users_by_id
from services.zendesk.base_custom_fields import PixCustomFieldsCreator, CardCustomFieldsCreator
from services.zendesk.zen_var import STANDARD_TICKET_COMMENT_PENDENCY
from utils import ResultHandle, PIX, HIBRIDO
from services.logger import create_logger
from models.refund_log import *
from Config.vars import HYBRID_CASE_DEBIT
from utils import enterprise_code_of_the_branch
from services.zendesk.references.map_ids import SENDER_AND_RECEIVER 

if __name__ == '__main__': 
    zen_service_logger = create_logger('')
    zen_service_logger.setLevel(10)
else: 
    zen_service_logger = create_logger(__name__, without_handler = True)


def create_zendesk_ticket(refund_info_old: Union[EstornoLog, Dict], env: str = "prd") -> ResultHandle:

    zen_service_logger.info("Iniciando criação de ticket zendesk")
    refund_info = refund_info_old if isinstance(refund_info_old, EstornoLog) else turn_estorno_log_to_dataclasse(refund_info_old)

    payload = make_create_ticket_payload(refund_info, env)
    result = create_ticket(payload, env)

    if result.failure:
        zen_service_logger.critical("Não foi possível criar ticket no zentesk")
    
    return result

def make_create_ticket_payload(refund_info: EstornoLog, env: str) -> Dict:

    text = STANDARD_TICKET_COMMENT_PENDENCY.format(refund_info = refund_info)

    raw_payload = Path(__file__).parent / "references"/ "template_payload_create_ticket.json"
    payload = loads(raw_payload.read_bytes())

    payload["ticket"]["custom_fields"] = fill_custom_fields(refund_info, env)
    payload["ticket"]["comment"]["html_body"] = text
    payload["ticket"]["subject"] = f"RPA - Estorno - valor {refund_info.valorEstorno} - {refund_info.nomeCliente}"
    payload["ticket"]["ticket_form_id"] = SENDER_AND_RECEIVER[env]["ticket_form_id"]

    if env == "prd":
        payload["ticket"]["group_id"] = SENDER_AND_RECEIVER[env]["group_id"]
        payload["ticket"]["brand_id"] = SENDER_AND_RECEIVER[env]["brand_id"]
    
    if env == "homo": 
        payload["ticket"]["assignee_id"] = SENDER_AND_RECEIVER[env]["assignee_id"]
        payload["ticket"]["brand_id"] = SENDER_AND_RECEIVER[env]["brand_id"]
        
    
    payload["ticket"]["requester_id"] = find_user_id_by_email(refund_info.codigoFilialDestino, env)
    payload["ticket"]["submitter_id"] = find_user_id_by_email(refund_info.codigoFilialDestino, env)

    return payload

def fill_custom_fields(refund_info: EstornoLog, env: str) -> List[Dict]:

    if ((refund_info.bandeira.lower() in PIX) or 
    (refund_info.bandeira.lower() in HIBRIDO) or 
    (HYBRID_CASE_DEBIT in refund_info.bandeira.lower())):
        zen_service_logger.info("Ticket para Pix")
        custom_fields = PixCustomFieldsCreator(refund_info, env)
    else:
        zen_service_logger.info("Ticket para Cartão")
        custom_fields = CardCustomFieldsCreator(refund_info, env)

    custom_fields.create_list()

    return custom_fields.fields

def find_user_id_by_email(branch_num: Union[str, int], env: str = "prd", default_id: int = 0):
    
    branch_num = int(branch_num) if isinstance(branch_num, str) else branch_num

    if branch_num >= 1 and branch_num <= 9:
        branch_num = f"0{branch_num}"


    if enterprise_code_of_the_branch(branch_num) == "pm":
        user_email_pattern = f"emp{branch_num}@pmenos.com.br"

    if enterprise_code_of_the_branch(branch_num) == "exf":
        user_email_pattern = f"ef{branch_num}@pmenos.com.br"
    
    if env == "homo":
        user_email_pattern = "<EMAIL>"

    users_matched_res = search_users_by_pattern(user_email_pattern, env)
    if users_matched_res.failure:
        zen_service_logger.critical("Erro ao procurar usuário")
        raise Exception("Falha na requisição de buscar usuário")

    for user in users_matched_res.data["users"]:
        if user_email_pattern in user["email"]:
            return user["id"]
    
    
    zen_service_logger.info("Usuário não foi encontrado")

    if default_id == 0:
        zen_service_logger.critical("Sem usuário alternativo definido")
        raise Exception(f"Sem usuário para o padrão: {user_email_pattern}")
    else:
        zen_service_logger.info(f"Usuário alterativo será usado {default_id}")
    
    return default_id

if __name__ == "__main__":

    # branch_num = 382
    # env = "prd"
    # x = find_user_id_by_email(branch_num, env) 
    #27484946863117

    users_matched_res = search_users_by_id(27484946863117)



    from Connections.search_db import SearchDb
    finder = SearchDb()
    
    refund_info_raw = finder.search_refund_by_refund_id(293498)
    refund_info = estorno_venda_log_orm(refund_info_raw[0][0])
    zen_ticket_res = create_zendesk_ticket(refund_info, env = "prd")
        

    print("fim")
    
    