import os
import json
from typing import Callable, TypeVar, <PERSON>, Dict, Tuple, Any
from Connections.search_db import SearchDb
from models.branch_id_acquirer import *
from models.refund_log import *
from services.multi_report_handler import log_report
from Config.database import query_one_db, query_all_db
import Config.vars as vars
from logging import getLogger
from services.logger import create_logger
from functools import wraps
from datetime import datetime, timedelta
from models.result_handle import ResultHandle
from traceback import format_exc


if __name__ == '__main__': 
    utils_logger = create_logger('')
    utils_logger.setLevel(20)
else: 
    utils_logger = create_logger(__name__, without_handler = True)

PIX_PAGA_LEVE_LABELS = ['pix pagale', 'pix pagaleve']



CARTAO = ['elo', 'visa', 'visa emv', 'visa cielo', 
        'electron','visa elect', 'electron emv', 'electron cielo', 
        'mastercard','mastercard deb',  'mastercard emv', 'mastercard cielo','mastercard safra', 
        'maestro', 'maestro emv', 'maestro cielo',
        'elo debito', 'elo credito','elo credit', 'valetik emv',
        'amex', 'amex cielo', 'american express', 'visa electron', 'caixa digital', 'hipercard', 'discover',
        "aura", "diners cie", "credi-shop", "credi shop", "credishop"]

REDE = ['hipercard l0500']

PIX = ['merc paggo','pix mercado pago','pix mercad','pix','pix vindi', 'pix itau', 'merc paggo pix']
PIX.extend(PIX_PAGA_LEVE_LABELS)

HIBRIDO = ['nupay']

WIND = ["WIND"]

GETNET_INVALIDOS = ['002', 
'003', '005', '008', '009', '010', '013', '014', '015', '023', '024', '025', '026', '028', '029', '030', '061', 
'062', '063', '064', '067', '069', '070', '102']

GETNET_VALIDOS = ['000', '020', '055', '072', '054']

CIELO_INVALIDOS = ['2','5','7','10','51','54','56','72','77','99','101','102','103','104','105','106',
'108','109','115','120','200','201','203','206','209','214','215','216','217','218','219','221','222','223','224','225',
'476','477','800','801','802','803','823','999']

CIELO_VALIDOS = ['1','17']



XML_MSG_GETNET_POST_CANCELAMENTO = '''
    <soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:br="http://br.com.getnet.reqcanc.ws.service/">
        <soapenv:Header/>
        <soapenv:Body>
            <br:requestCancelTransation>
                <!--Optional:-->
                <arg0>
                    <authentication>
                        <username>{}</username>
                        <password>{}</password>
                    </authentication>
                    <cancelTransaction>
                        <branch>{}</branch>
                        <terminal>{}</terminal>
                        <autorization>{}</autorization>
                        <date>{}</date>
                        <modality>{}</modality>
                        <amount>{:.2f}</amount>
                        <currency_code>986</currency_code>
                        <inst_num>{}</inst_num>
                        <nsu>{}</nsu>
                        <cancel_amount>{:.2f}</cancel_amount>
                    </cancelTransaction>
                </arg0>
            </br:requestCancelTransation>
        </soapenv:Body>
        </soapenv:Envelope>
'''

XML_MSG_GETNET_RECEIVE = '''
    <soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:br="http://br.com.getnet.reqcanc.ws.service/">
    <soapenv:Header/>
    <soapenv:Body>
        <br:queryCancelTransationByProtocol>
            <!--Optional:-->
            <arg0>
                <authentication>
                    <username>{}</username>
                    <password>{}</password>
                </authentication>
                <queryByProtocol>
                    <protocol>{}</protocol>
                </queryByProtocol>
            </arg0>
        </br:queryCancelTransationByProtocol>
    </soapenv:Body>
    </soapenv:Envelope>
'''


def get_application_root_path():
    return os.path.dirname(os.path.dirname(os.path.abspath(__file__)))

def get_config():
    path = get_application_root_path()
    return json.load(open(path + '\\config.json'))

def is_a_mapped_payment_method(payment_method: str, estorno_service, refund_info: Dict):
    
    payment_method_simplified = get_tipo(payment_method)
    if payment_method_simplified != -1: return payment_method_simplified
    else:
        
        refund_info['bandeira'] = payment_method if payment_method is not None else ''
        refund_info['motivo'] = f'Meio de pagamento não mapeado - {refund_info["bandeira"]}'
        refund_info['status'] = vars.STATUS_PENDENCY_FOR_CSC
        
        estorno_service.record_refund_processment(refund_info)
        log_report(f"Estorno {refund_info['idEstorno']}:Bandeira do cartao nao mapeada") 

    return -1
        
def get_tipo(bandeira:str):
    
    if bandeira is None: return -1

    payment_method = bandeira.lower().strip()

    if payment_method in CARTAO:
        return 'cartao'   
    elif payment_method in PIX:
        return 'pix'    
    elif payment_method in HIBRIDO:
        return 'hibrido'    
    else:
        return -1


def get_app_root_path() -> (str):
        app_root_path = os.path.dirname(os.path.dirname(__file__))
        return app_root_path

def del_app_root_path():
    path_output = get_app_root_path()
    
    try:
        os.remove(path_output + '\output.csv')
    except Exception:
        pass


class FileDirHandler():

    def __init__(self, default_download_path):
        self.default_download_path = default_download_path                
        super().__init__()
    
    def create_dir(self, filepath = None):
        download_path = self.default_download_path
        filepath = download_path if not bool(filepath) else filepath               
        if (not os.path.exists(filepath)):
            os.mkdir(filepath)
        
    def clean_dir(self, filepath = None):
        download_path = self.default_download_path        
        filepath = download_path if not bool(filepath) else filepath
        for filename in os.listdir(filepath):
            try:
                os.remove('{}/{}'.format(filepath, filename))
            except Exception as e:
                raise e
    




class ResultHandle():

    def __init__(self, success: bool, data, error_description: str, success_description=None):
        """ ### Controlar o fluxo de erros

        Args:
            success (bool): Booleano que marca se a operação ocorreu bem
            data (any): Resultado esperado
            error_description (str): Descriação do erro caso houver
            wait (bool): Deve
        """
        self.success = success
        self.success_description = success_description
        self.error_description = error_description
        self.data = data
        self.wait = False

    @property
    def failure(self):
        return not self.success

    def __str__(self):
        if self.success:
            return f'[Success]'
        else:
            return f'[Failure]: "{self.error_description}"'

    def __repr__(self):
        if self.success:
            return f"<Result success={self.success}>"
        else:
            return f'<Result success={self.success}, message="{self.error_description}">'

    @classmethod
    def Fail(cls, error_msg, data=None):
        return cls(False, data=data, error_description=error_msg, success_description=None)

    @classmethod
    def Ok(cls, data=None, success_description=None):
        return cls(True, data=data, success_description=success_description, error_description=None)

    @classmethod
    def Waiting(cls, error = None, data=None):
        cls_obj = cls(False, data=data, error_description=error, success_description=None)
        cls_obj.wait = True
        return cls_obj



T = TypeVar('T', bound=Callable[..., Any])
def exception_report(_func: T = None, *, logmsg: str = None, module_name: str = None)-> T:
    """ ## Informar qual foi a exeção que ocorreu de modo mais conciso

    Args:
        `_func` (Callable, optional): Caso não seja passado nenhum argumento-chave\n
        essa variável será a referência da função que será decorada. Se pelo menos \n
        1 argumento-chave por passado essa variável será igual a None \n \n

        `logmsg` (str, optional): Mensagem personalizada para a exeção \n
        `module_name` (str, optional): Módulo com o logger personalizado para emitir a messagem \n
    """

    def decorate_with_log(func: Callable[..., T]) -> Callable[..., Union[T, ResultHandle]]:
        @wraps(func)
        def wrapper(*args, **kwargs) -> Union[T, ResultHandle]:
            module = module_name if module_name is not None else func.__module__
            logger = getLogger(module)

            msg = logmsg if logmsg is not None else f"Erro na função {func.__name__}"
            
            try:
                value = func(*args, **kwargs)
                return value
            except Exception as error:
                logger.error(msg)
                raise error

        return wrapper
        

    if _func is None:
        return decorate_with_log
    else:
        return decorate_with_log(_func)


def enterprise_code_of_the_branch(branch_num: Union[int, str]) -> str:
    """Indicar qual é o código empresa para a filial

    Args:
        branch_num (Union[int, str]): Número da filial

    Returns:
        str: Código de da empresa: E.g. (`exf` ou `pm`)
    """        

    enterprise_code_query = """
    SELECT EMPR_CD_EMPRESA FROM Cosmos_v14b.dbo.FILIAL NOLOCK
    WHERE FILI_CD_FILIAL = ?
    """

    if isinstance(branch_num, str): branch_num = int(branch_num)

    try:
        enterprise_code = query_one_db(enterprise_code_query, branch_num)
        assert isinstance(enterprise_code, dict), f"Sem código de empresa para filial {branch_num}"
    except Exception as error:
        print(f"Erro ao verificar qual a empresa da filial {branch_num}")
        print(f"Detalhes: {error}")
        raise error

    if enterprise_code["EMPR_CD_EMPRESA"] == 2:
        return vars.EXTRA_FARMA_CASE
    else:
        return vars.PAGUE_MENOS_CASE

DIFF_VALUE_MARGIN = 1
class DiffValueError(Exception):
    def __init__(self, message):
        self.message = message

def verify_diff_values(obj_estorno_log: Dict, 
                      payment_method_value: float) -> Dict:    

    if obj_estorno_log["valorTotalVenda"] == obj_estorno_log["valorEstorno"]:

        return diff_values_in_a_total_refund(obj_estorno_log, float(payment_method_value))

    else:
        return diff_values_in_a_parcial_refund(obj_estorno_log, float(payment_method_value))


def diff_values_in_a_total_refund(obj_estorno_log: Dict, 
                      payment_method_value: float) -> Dict:
    """Verificar se houve divergência de valores entre troca-dev e outras fontes
    quando é um esotrno total. 
    
    Essa função existe para evitar o envio de um estorno total
    menor do que foi pedido pelo cliente dentro da margem de erro. 

    Essa situação ocorre quando vem do troca-dev um pedido de estorno total
    (valor do cupom igual ao valor do estorno) e ao verificar o valor do finalizador
    em outra fonte (ex: VTEX) vem um valor maior do que o susposto estorno total. 
    Dessa maneira, fica a dúvida, se é um estorno total, por que o valor do finalizador
    está diferente do finaliador, será que na realidade foi um estorno parcial?


    Args:
        obj_estorno_log (Dict): Dados do pedido de estorno
        payment_method_value (float): Valor do meio de pagamento no qual será feito o estorno

    Raises:
        Exception: _description_

    Returns:
        Dict: Dados do pedido de estorno
    """
    
    

    if abs(obj_estorno_log["valorTotalVenda"] - payment_method_value) <= DIFF_VALUE_MARGIN:
        obj_estorno_log['valorEstorno'] = payment_method_value

    else:
        raise DiffValueError("Divergência de valores entre troca-dev e outras fontes")
    
    return obj_estorno_log

def diff_values_in_a_parcial_refund(obj_estorno_log: Dict, 
                      payment_method_value: float) -> Dict:
    
    if ((obj_estorno_log["valorEstorno"] - payment_method_value > 0) 
    and (obj_estorno_log["valorEstorno"] - payment_method_value <= DIFF_VALUE_MARGIN)):
        
        obj_estorno_log['valorEstorno'] = payment_method_value

    if ((obj_estorno_log["valorEstorno"] - payment_method_value > 0) 
    and (obj_estorno_log["valorEstorno"] - payment_method_value > DIFF_VALUE_MARGIN)):
        
        raise DiffValueError("Divergência de valores entre troca-dev e outras fontes")

    return obj_estorno_log


def get_branch_id_in_acquirer(refund_info: EstornoLog , acquirer_code: int) -> Tuple[Union[vars.ResultHandle, BranchIdentificationInAcquirer], EstornoLog]:

    if acquirer_code not in (102, 108, 95):
        raise NotImplementedError(f"Não há ORM para a adquirente informada: {acquirer_code}")
    
    if acquirer_code == 95:
        result = BranchIdentificationInAcquirer(terminal = None, establishment = None, branch = refund_info.codigoFilialOrigem)
        return result, refund_info

    query_terminal_and_establishment = """
            select num_terminal, cod_estabelecimento, cod_filial
            from scope.clusterscopeCNF{db_cluster}.dbo.TerminalConfiguration nolock
            where 
            num_terminal <> ''
            AND cod_filial = '{branch_z_padded}'
            AND cod_rede = '{acquirer_code}'
            """

    if refund_info.codigoFilialOrigem <= 446: db_cluster = 1
    elif refund_info.codigoFilialOrigem > 446 and refund_info.codigoFilialOrigem <= 892: db_cluster = 2
    elif refund_info.codigoFilialOrigem > 892 and refund_info.codigoFilialOrigem <= 1338: db_cluster = 3
    elif refund_info.codigoFilialOrigem > 1338 and refund_info.codigoFilialOrigem <= 1530: db_cluster = 4
    if refund_info.codigoFilialOrigem >= 7000: db_cluster = 5

    branch = str(refund_info.codigoFilialOrigem).zfill(4)

    query = query_terminal_and_establishment.format(db_cluster = db_cluster,
                                                    branch_z_padded = branch,
                                                    acquirer_code = acquirer_code)
    
    branch_in_acquirer = query_all_db(query)

    if len(branch_in_acquirer) == 0:
        msg = f"Não foi encontrar os dados de terminal e estabelecimento para a filial {branch}"
        refund_info.motivo = msg
        refund_info.status = vars.STATUS_PENDENCY_FOR_CSC
        utils_logger.warning(msg)
        return vars.ResultHandle.Fail(msg), refund_info

    if len(branch_in_acquirer) > 1:
        utils_logger.warning("Mais de um registro de terminal e estabelecimento encontrados")
        utils_logger.warning("O primeiro será usado")


    if acquirer_code == 102:
        result = load_orm_branch_id_in_cielo_tef(branch_in_acquirer[0])
    if acquirer_code == 108:
        result = load_orm_branch_id_in_getnet_tef(branch_in_acquirer[0])

    return result, refund_info


def avoid_legacy_api_duplicates(id_refund: Union[int, str]) -> bool:

    finder = SearchDb()

    id_refund = str(id_refund) if isinstance(id_refund, int) else id_refund
    
    result, _ = finder.search_refund_by_refund_id(id_refund)
    if result is not None:
        utils_logger.info(f"Pedido de estorno {id_refund} ja foi processado")
        utils_logger.info("Duplicidade da consulta da API") 
        return True
    else:
        return False


def is_there_a_partial_refund_to_wait(current_refund: Dict) -> bool:
    """Verifica se já existe um estorno parcial para a mesma transação e 
    se o intervalo entre o pedido de estorno atual e o 
    pedido de estorno já processado é menor que 1 dia será retornado True
    indicando que deve-se aguardar o processamento do estorno parcial
    antes de prosseguir com o estorno atual.

    Args:
        current_refund (Dict): Informações do pedido de estorno atual

    Returns:
        bool: Confirmação se deve pular o estorno atual ou prosseguir com ele
    """

    finder = SearchDb()
    already_refunded = finder.is_there_already_a_refund_for_the_same_transaction(current_refund['codigoFilialOrigem'], current_refund['codigoAutorizacao'])
    if already_refunded.failure: return True

    if len(already_refunded.data) > 0:
        interval: timedelta = datetime.today() - already_refunded.data[0]['DATA_DEVOLUCAO']

        if interval.days  <= 1:
            utils_logger.info("Existe um pedido de estorno parcial para a mesma transação e o intervalo entre os pedidos é menor que 1 dia") 
            return True

    return False



def traceback_of_last_n_lines(lines_back: int = 5) -> str:
    tb_lines = format_exc().splitlines()
    limited_traceback = '\n'.join(tb_lines[-lines_back:])
    return limited_traceback

def fill_lacking_information(current_refund: Dict) -> Dict:
    
    query = f"""
    SELECT
    CAB.ID_VENDA_CAB        AS NumeroPreVenda
    ,NUMERO_IKEDA         AS Num_VTEX    
    ,CONVERT(CHAR(1),ISNULL(PLANO_ASSINATURA,'N')) AS IsPlanoAssinatura
    ,CAB.CANAL_VENDAS         AS CanalVendas  
    ,CC2.CACR_NM_CARTAO        AS NomeBandeira 
    ,cab.VC_CUPOM_FILIAL_REGISTRO     AS FilialBaixa
    ,cab.VC_CUPOM_ECF_REGISTRO      AS PdvBaixa 
    

            ,MVTC_NR_FIL                AS FILIAL_ORIGEM,
            FILI_ORIG.FILI_TN_CNPJ             AS CNPJ_FILIAL_ORIGEM,
            MVTC_CD_FILIAL_MOV              AS FILIAL_DESTINO,
            FILI_DEST.FILI_TN_CNPJ             AS CNPJ_FILIAL_DESTINO,
            MVTC_NR_CPM_ECF_VDA              AS NUMERO_CUPOM,
            NUMERO_COO                AS NUMERO_COO,
            MVTC_NR_ECF_MOV               AS PDV_DEVOLUCAO,
            CLI.FICL_NM_CLIENTE                                                      AS NOME_CLIENTE,
            TROCDEV.NUMERO_IKEDA_VTEX            AS NUMERO_VTEX,
            TROCDEV.NUMERO_PEDIDO_DELIVERY           AS NUMERO_DELIVERY,
            MVTC_DT_CPM                AS DATA_MOVIMENTO,
            TROCDEV.TOTAL_CUPOM_ORIGEM            AS VALOR_CUPOM,             
        ISNULL(DPOS.VALOR_DIGITADO, SALDO.VALOR_IMPRESSO_FINALIZADOR)      AS VALOR_ESTORNO,
            MVTC_DH_INCLUSAO               AS DATA_DEVOLUCAO,            
        ISNULL(DPOS.NSU,FLZ.FLDE_NR_NSU_TEF)          AS NSU_TEF,             
        ISNULL(DPOS.NSU,FLZ.FLDE_NSU_AUTORIZADORA_TEF)       AS NSU_HOST,   
            LEFT(COALESCE(CC.CACR_NM_CARTAO, FLZ.FLDE_NM_REDE_TEF),20)     AS BANDEIRA, 
        CASE 
    WHEN ISNULL(FONE_CLIENTE,'') <> '' THEN FONE_CLIENTE     
    ELSE CLI.FICL_TN_TELCELULA
        END                  AS TELEFONE_CLIENTE,
            CASE 
    WHEN ISNULL(EMAIL_CLIENTE,'') <> '' THEN EMAIL_CLIENTE
    ELSE CLI.FICL_TX_EMAIL
        END                  AS EMAIL_CLIENTE,
            -- CRIAR 
            TROCDEV.NUMERO_PRE_VENDA             AS NUMERO_PRE_VENDA,
            ISNULL(TROCDEV.E_PLANO_ASSINATURA,'N')         AS E_PLANO_ASSINATURA,
            TROCDEV.CANAL_VENDAS              AS CANAL_VENDAS,
            TROCDEV.NUMERO_CARTAO_SEMPRE            AS NUMERO_CARTAO_SEMPRE,
            FLZ.FLDE_QT_PAR_CAR              AS FLDE_QT_PAR_CAR,
            CASE FLZ.FLDE_TP_POS       
    WHEN 'M' THEN 'S'
    WHEN 'A' THEN 'N'
    ELSE NULL      
    END                  AS FLDE_TP_POS,
                FLZ.E_PAGAMENTO_UNIFICADO           AS E_PAGAMENTO_UNIFICADO,
        TROCDEV.ID_DEVTROCA_CAB             AS ID_DEVTROCA_CAB,
        FLZ.COO_PAGAMENTO_UNIFICADO            AS COO_PGTO_UNICIADO,
        EST.CODIGO_ESTABELECIMENTO_TEF           AS CODIGO_ESTABELECIMENTO,
        EST.ID_PIX                AS ID_PIX
        FROM COSMOSFL..MVENT_TRCDEV_CAB TROCDEV WITH (NOLOCK)
        INNER JOIN cosmos_v14b..FILIAL FILI_DEST WITH (NOLOCK) ON TROCDEV.MVTC_CD_FILIAL_MOV = FILI_DEST.FILI_CD_FILIAL
        INNER JOIN cosmos_v14b..FILIAL FILI_ORIG WITH (NOLOCK) ON TROCDEV.MVTC_NR_FIL = FILI_ORIG.FILI_CD_FILIAL
        LEFT JOIN COSMOSFL..MVENT_TRCDEV_SALDO_FINALIZ SALDO WITH (NOLOCK) ON TROCDEV.ID_DEVTROCA_CAB = SALDO.ID_DEVTROCA_CAB
    INNER JOIN CosmosFL..FLZ_VDA EST ON EST.FLVD_DT_MOV = TROCDEV.MVTC_DT_CPM
            AND EST.FLVD_CD_FILIAL_MOV=TROCDEV.MVTC_NR_FIL
            AND EST.FLVD_NR_ECF_MOV=TROCDEV.MVTC_NR_ECF
            AND EST.FLVD_CT_VDA=TROCDEV.NUMERO_COO
            AND EST.FLVD_CT_FLZ_VDA = SALDO.ID_FLZ_VENDA         
        LEFT JOIN COSMOSFL..FLZ_DEV FLZ WITH(NOLOCK) 
        ON 
        (
    TROCDEV.MVTC_DT_MOV = FLZ.FLDE_DT_MOV
    AND TROCDEV.MVTC_CD_FILIAL_MOV = FLZ.FLDE_CD_FILIAL_MOV
    AND TROCDEV.MVTC_NR_ECF_MOV = FLZ.FLDE_NR_ECF_MOV  
    AND SALDO.ID_MVENT_TRCDEV_SALDO_FINALIZ = FLZ.FLDE_CT_FLZ_DEV
        )
    LEFT JOIN COSMOSMKT..FIDELIZ_CLI CLI WITH(NOLOCK)
    ON(
    TROCDEV.NUMERO_CARTAO_SEMPRE = CLI.FICL_SQ_CLIENTE
    )
    LEFT JOIN COSMOSFL..MVENT_TRCDEV_DIGITACAO_POS DPOS WITH(NOLOCK)
    ON(
    SALDO.ID_DEVTROCA_CAB = DPOS.ID_DEVTROCA_CAB
    AND SALDO.CT_VDA = DPOS.SEQUENCIAL
    AND SALDO.ID_FLZ_VENDA = DPOS.SEQ_FINALIZADORA   
    )
    LEFT JOIN COSMOS_V14B..CARTAO_CREDITO CC WITH(NOLOCK)
    ON(
    DPOS.CODIGO_BANDEIRA = CC.CACR_SQ_CARTAO 
    )
    LEFT JOIN  Cosmos_v14b..AUTORIZAD_CARTAO_CTF aut  WITH(NOLOCK)
    ON(aut.CODIGO_REDE_SCOPE =  EST.REDE_SCOPE
    AND aut.CACR_SQ_CARTAO = EST.FLVD_CT_TP_CCT)
    LEFT JOIN COSMOSFL..MVENT_TRCDEV_DIGITACAO_CUPOM_DIGITADO DFLZ WITH(NOLOCK)
    ON(
    TROCDEV.ID_DEVTROCA_CAB = DFLZ.ID_DEVTROCA_CAB
    )
    LEFT JOIN CosmosFL.PRE_VENDA.VENDA_CAB CAB WITH(NOLOCK)
    ON TROCDEV.MVTC_DT_CPM = CAB.VC_CUPOM_DATA_REGISTRO AND
        TROCDEV.MVTC_NR_FIL = CAB.VC_CUPOM_FILIAL_REGISTRO AND
        TROCDEV.MVTC_NR_ECF = CAB.VC_CUPOM_ECF_REGISTRO AND
        TROCDEV.MVTC_NR_CPM_ECF_VDA = CAB.VC_CUPOM_NUMERO
    LEFT JOIN CosmosFL.PRE_VENDA.VENDA_FLZ PVFLZ WITH(NOLOCK)    
    ON(
    CAB.ID_VENDA_CAB = PVFLZ.ID_VENDA_CAB
    ) 
    LEFT JOIN cosmos_v14b..CARTAO_CREDITO CC2
    ON 
    (
    PVFLZ.CACR_SQ_CARTAO = CC2.CACR_SQ_CARTAO
    )

        WHERE TROCDEV.ID_DEVTROCA_CAB = {current_refund['idTrocaCab']}
        AND SALDO.CODFINALIZADOR = 3
    AND ((SALDO.SIGLA_FINALIZADOR IN('POS','CART')) OR (SALDO.SIGLA_FINALIZADOR = 'POS2' AND SALDO.PGTO_EFETUADO_POS = 'S'))
    AND TROCDEV.MVTC_TP_ENT_CAB = 'D'
    AND DFLZ.ID_DEVTROCA_CAB IS NULL"""

    try:
        result = query_all_db(query)

        if len(result) == 0:
            utils_logger.error(f"Não foi possível obter informações adicionais para o estorno id_dev_troca_cab ={current_refund['idTrocaCab']}")
            return current_refund
        
        elif len(result) == 1:
            utils_logger.info(f"Obtidas informações adicionais para o estorno id_dev_troca_cab = {current_refund['idTrocaCab']}")
            
            current_refund['bandeira'] = result[0]['NomeBandeira'] if result[0]['NomeBandeira'] is not None else ''
            current_refund['numeroPedidoVTEX'] = str(result[0]['Num_VTEX']) if result[0]['Num_VTEX'] is not None else ''
            return current_refund
        
        elif len(result) > 1:

            for refund in result:
                if float(refund['VALOR_ESTORNO']) == current_refund['valorEstorno']:
                    current_refund['bandeira'] = result[0]['NomeBandeira'] if result[0]['NomeBandeira'] is not None else ''
                    current_refund['numeroPedidoVTEX'] = str(result[0]['Num_VTEX']) if result[0]['Num_VTEX'] is not None else ''
                    return current_refund
        
        utils_logger.error(f"Não foi possível obter informações adicionais para o estorno id_dev_troca_cab ={current_refund['idTrocaCab']}")
        return current_refund
    
    except Exception as e:
        utils_logger.error(f"Erro ao obter informações adicionais para o estorno id_dev_troca_cab ={current_refund['idTrocaCab']}. Erro: {e}")
        return current_refund
    
    
if __name__ == "__main__":

    x = get_branch_id_in_acquirer(929, "102")


