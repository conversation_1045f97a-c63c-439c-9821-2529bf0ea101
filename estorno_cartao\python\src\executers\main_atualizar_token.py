from Connections.search_db import SearchDb
from Connections.update_db import UpdateDb
from services.cielo_service import ServiceCielo


class AtualizarToken:
    
    def __init__(self,config) -> None:
        self.search_db = SearchDb()
        self.update_db = UpdateDb()
        self.service_cielo = ServiceCielo(config)

    def execute(self):

        refresh = self.search_db.search_token('Refresh_Token_Cielo')
        acesstoken, refreshtoken = self.service_cielo.atulizar_token_cielo(refresh,'Pague Menos')

        self.update_db.update_method([acesstoken,'Access_Token_Cielo'])
        self.update_db.update_method([refreshtoken,'Refresh_Token_Cielo'])

        refresh = self.search_db.search_token('Refresh_Token_Cielo_Ext')
        
        acesstokenext, refreshtokenext = self.service_cielo.atulizar_token_cielo(refresh, 'Extrafarma')

        self.update_db.update_method([acesstokenext,'Access_Token_Cielo_Ext'])
        self.update_db.update_method([refreshtokenext,'Refresh_Token_Cielo_Ext'])

    def execute_ext(self):  
        
        refresh = self.search_db.search_token('Refresh_Token_Cielo_Ext')  
        
        acesstokenext, refreshtokenext = self.service_cielo.atulizar_token_cielo_ext(refresh)

        self.update_db.update_method([acesstokenext,'Access_Token_Cielo_Ext'])
        self.update_db.update_method([refreshtokenext,'Refresh_Token_Cielo_Ext'])