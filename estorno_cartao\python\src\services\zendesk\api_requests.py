from pathlib import Path
import requests
import sys
from typing import <PERSON>ple, Dict

def local_execution_with_others_projects_modules(current_file: str, directory_to_go_back: int = 1) -> Path:
    """
    If in a local execution the current module uses resources from another \n
    module that is in the same project i.e.(this module is in the same \n
    directory as the other module) and they aren't in the same level. \n
    So you need to add the common dictory between the modules to the Paths of execution \n
    
    Args:
        `current_file` (str): file path who calls the other modules

    Returns:
        `Path`: common directory Path
    """
    
    src_folder = Path(current_file.split(":")[0].upper() + ":" + current_file.split(":")[1])
    for _ in range(directory_to_go_back): src_folder = src_folder.parent
    if src_folder.__str__() not in sys.path: sys.path.append(str(src_folder))
    if Path.cwd().__str__() not in sys.path: sys.path.append(Path.cwd().__str__())

    return src_folder

if __name__ == "__main__":
    src_folder = local_execution_with_others_projects_modules(__file__, 3)

import utils
from services.logger import create_logger
from services.zendesk import zen_var
from services.zendesk import zen_tools


if __name__ == '__main__': 
    zen_request = create_logger('')
    zen_request.setLevel(10)
else: 
    zen_request = create_logger(__name__, without_handler = True)



@utils.exception_report
def get_field_info(field_id: int, env: str = "prd") -> utils.ResultHandle:
    """Obter informações do campo

    Args:
        field_id (int): id do campo
        env (str, optional): código do ambiente que será acessado. Defaults to "prd".

    """

    config = utils.get_config()

    url = zen_var.FIELD_INFO_END_POINT.format(
            subdomain = config["api"]["zendesk"][env]["subdomain"],
            field_id = field_id)

    auth = zen_tools.make_auth(config, env)

    response = requests.get(url, auth=auth)

    result = zen_tools.validate_request(response)
    if result.failure:
        return result

    return result


@utils.exception_report
def get_ticket_info(ticket_id: int, env: str = "prd") -> utils.ResultHandle:

    config = utils.get_config()

    url = zen_var.TICKET_INFO_END_PONT.format(
            subdomain = config["api"]["zendesk"][env]["subdomain"],
            ticket_id = ticket_id)

    auth = zen_tools.make_auth(config, env)

    headers = {
	"Content-Type": "application/json",
    }

    response = requests.get(url, 
                            auth=auth, 
                            headers=headers)
    

    result = zen_tools.validate_request(response)
    
    if result.failure:
        zen_request.error("Erro na requisição")
        zen_request.error(f"Detalhes do erro: {result.error_description}")
        return result

    return result


@utils.exception_report
def get_ticket_info_with_rate_limit(ticket_id: int, env: str = "prd") -> Tuple[utils.ResultHandle, Dict]:

    config = utils.get_config()

    url = zen_var.TICKET_INFO_END_PONT.format(
            subdomain = config["api"]["zendesk"][env]["subdomain"],
            ticket_id = ticket_id)

    headers = {
	"Content-Type": "application/json",
    }

    auth = zen_tools.make_auth(config, env)

    response = requests.get(url, 
                            auth=auth, 
                            headers=headers)

    result = zen_tools.validate_request(response)
    
    if result.failure:
        zen_request.error("Erro na requisição")
        zen_request.error(f"Detalhes do erro: {result.error_description}")
        return result, None

    return result, response.headers._store


@utils.exception_report
def create_ticket(payload: int, env: str = "prd") -> utils.ResultHandle:

    config = utils.get_config()

    url = zen_var.CREATE_TICKET_END_POINT.format(
            subdomain = config["api"]["zendesk"][env]["subdomain"])
    
    auth = zen_tools.make_auth(config, env)

    headers = {
	"Content-Type": "application/json",
    }

    response = requests.post(url, 
                            auth=auth,
                            headers =headers, 
                            json=payload)

    result = zen_tools.validate_request(response)
    if result.failure:
        return result

    return result


@utils.exception_report
def search_users_by_pattern(pattern_str: str, env: str = "prd") -> utils.ResultHandle:

    config = utils.get_config()

    url = zen_var.SEARCH_USER_END_POINT.format(
            subdomain = config["api"]["zendesk"][env]["subdomain"])

    auth = zen_tools.make_auth(config, env)

    pattern_parameter = f"?query={pattern_str}"

    url = url + pattern_parameter

    headers = {
	"Content-Type": "application/json",
    }

    response = requests.get(url, 
                            auth = auth,
                            headers = headers)

    result = zen_tools.validate_request(response)
    if result.failure:
        return result

    return result

@utils.exception_report
def search_users_by_id(user_id: str, env: str = "prd") -> utils.ResultHandle:

    config = utils.get_config()

    url = zen_var.SEARCH_USER_END_POINT_BY_ID.format(
            subdomain = config["api"]["zendesk"][env]["subdomain"],
            user_id = user_id)

    auth = zen_tools.make_auth(config, env)

    headers = {
	"Content-Type": "application/json",
    }

    response = requests.get(url, 
                            auth = auth,
                            headers = headers)

    result = zen_tools.validate_request(response)
    if result.failure:
        return result

    return result



@utils.exception_report
def get_audits_for_ticket(ticket_id: int, env: str = "prd") -> Tuple[utils.ResultHandle, Dict]:

    config = utils.get_config()

    url = zen_var.TICKET_AUDITS_END_PONT.format(
            subdomain = config["api"]["zendesk"][env]["subdomain"],
            ticket_id = ticket_id)
    
    headers = {
	"Content-Type": "application/json",
    }

    auth = zen_tools.make_auth(config, env)

    response = requests.get(url, 
                            auth=auth,
                            headers=headers)

    result = zen_tools.validate_request(response)
    
    if result.failure:
        zen_request.error("Erro na requisição")
        zen_request.error(f"Detalhes do erro: {result.error_description}")
        return result, None

    return result, response.headers._store



@utils.exception_report
def update_ticket(ticket_id: int, payload: dict, env: str = "prd" ) -> Tuple[utils.ResultHandle, Dict]:

    config = utils.get_config()

    url = zen_var.TICKET_INFO_END_PONT.format(
            subdomain = config["api"]["zendesk"][env]["subdomain"],
            ticket_id = ticket_id)

    headers = {
	"Content-Type": "application/json",
    }

    auth = zen_tools.make_auth(config, env)

    response = requests.put(url, 
                            auth=auth, 
                            headers=headers,
                            json=payload)

    result = zen_tools.validate_request(response)
    
    if result.failure:
        zen_request.error("Erro na requisição")
        zen_request.error(f"Detalhes do erro: {result.error_description}")
        return result, None

    return result, response.headers._store




if __name__ == "__main__":





    print("fim")