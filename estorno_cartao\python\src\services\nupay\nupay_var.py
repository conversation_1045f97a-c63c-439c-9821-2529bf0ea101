from dataclasses import dataclass

NUPAY_REFUND_KEY = "refunds"
NUPAY_CANCELLED = "CANCELLED"
NUPAY_ERROR = "ERROR"
NUPAY_COMPLETED = "COMPLETED"
NUPAY_WAIT = "WAITING_PAYMENT_METHOD"
NUPAY_WAIT_REFUND = "REFUNDING"
NUPAY_REFUNDED = "REFUNDED"

# Tipos de erro de estorno
NUPAY_SYSTEM_ERROR = "SYSTEM"
NUPAY_UNKNOWN_ERROR = "UNKNOWN"
NUPAY_ERROR_OPERATION = "OPERATION"
NUPAY_INSUFFICENT_FUNDS = "INSUFFICIENT_FUNDS"
NUPAY_FULLY_REFUNDED = "FULLY_REFUNDED"
NUPAY_MAX_NUMBER_REACHED = "MAX_NUMBER_REACHED"


sale_abnormal_status_and_standard_msg = {
    NUPAY_CANCELLED: "Compra já cancelada na nupay",
    NUPAY_ERROR: "Venda com erro na nupay"
    }


type_of_refund_erros_and_standard_msg = {
    NUPAY_SYSTEM_ERROR:"Erro desconhecido na nupay",
    NUPAY_UNKNOWN_ERROR: "Erro desconhecido na nupay",
    NUPAY_ERROR_OPERATION: "Falha de inconsistência de informações. Revise se o valor e o período do pagamento estão corretos.",
    NUPAY_INSUFFICENT_FUNDS: "Saldo insuficiente para realizar a devolução",
    NUPAY_FULLY_REFUNDED: "O valor do compra ja foi integralmente devolvido anteriormente.",
    NUPAY_MAX_NUMBER_REACHED: "Já foi solicitada a quantidade máxima de estornos por pedido."
}

request_error_and_standard_msg = {
    400: None,
    401:"Acesso não autorizado a API",
    404: None,
    412: None,
    429: "Algum limite de uso da API foi atingido"
}



@dataclass
class TranslatedErrorItem:
    key_string: str
    translation: str
    full_error_msg: str

SALE_FULLY_REFUNDED_MSG = "Venda já foi completamente estornada"
translated_erros = (
    TranslatedErrorItem(
        "Invalid credential",
        "Credencial inválida",
        "Invalid credential is not valid to checkout"
    ),
    TranslatedErrorItem(
        "FULLY_REFUNDED",
        SALE_FULLY_REFUNDED_MSG,
        "Invalid This payment can not be refunded: FULLY_REFUNDED"
    ),
    TranslatedErrorItem(
        "exceeds the amount available",
        "Valor de estorno está maior do que o valor disponível",
        "Required refund amount exceeds the amount available"
    ),
    TranslatedErrorItem(
        "Invalid Refund amount",
        "Valor do pedido de estorno é inválido",
        "Invalid Refund amount"
    )
    
)