from pathlib import Path
import sys

def local_execution_with_others_projects_modules(current_file: str, directory_to_go_back: int = 1) -> Path:
    """
    If in a local execution the current module uses resources from another \n
    module that is in the same project i.e.(this module is in the same \n
    directory as the other module) and they aren't in the same level. \n
    So you need to add the common dictory between the modules to the Paths of execution \n
    
    Args:
        `current_file` (str): file path who calls the other modules

    Returns:
        `Path`: common directory Path
    """
    
    src_folder = Path(current_file.split(":")[0].upper() + ":" + current_file.split(":")[1])
    for _ in range(directory_to_go_back): src_folder = src_folder.parent
    if src_folder.__str__() not in sys.path: sys.path.append(str(src_folder))
    if Path.cwd().__str__() not in sys.path: sys.path.append(Path.cwd().__str__())

    return src_folder

if __name__ == "__main__":
    src_folder = local_execution_with_others_projects_modules(__file__, 3)

from services.pagaleve.paga_leve_api import get_checkout, get_payment, request_refund
from services.logger import create_logger
from services.vtex.vtex_validations import VtexValidator
from Connections.search_db import SearchDb
from models.refund_log import estorno_venda_log_orm
from services.pagaleve.paga_leve_service import process_pagaleve_refund


if __name__ == '__main__': 
    test_pglev_logger = create_logger('')
    test_pglev_logger.setLevel(10)
else: 
    test_pglev_logger = create_logger(__name__, without_handler = True)
    

if __name__ == "__main__":

    finder = SearchDb()
    
    refund_info_raw = finder.search_refund_by_refund_id_in_estorno_venda(332878)
    refund_info = estorno_venda_log_orm(refund_info_raw[0][0])

    if refund_info.codigoFilialOrigem > 7000:
        entreprise = "exf"
    else:
        entreprise = "pm"

    vtex_essencial_dict = {
                            "numeroPedidoVTEX":refund_info.numeroPedidoVTEX,
                            "valorEstorno": refund_info.valorEstorno,
                            "bandeira":refund_info.bandeira,
                            "valorTotalVenda": refund_info.valorTotalVenda
                          }

    vtex_referee  = VtexValidator(vtex_essencial_dict)
    vtex_sale_info, obj_estorno_log = vtex_referee.get_vtex_info()


    refund_info.valorEstorno = 1.99
    failure_checkout_id_1 = vtex_sale_info["nsu"][:-3] + "df6"
    failure_checkout_id_2 = "fkjhfjkhsdrfjkhf455454"
    refund_request_info, refund_info = process_pagaleve_refund(vtex_sale_info["nsu"], refund_info)

    print("fim")

