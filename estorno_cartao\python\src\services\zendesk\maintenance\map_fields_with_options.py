from pathlib import Path
import sys
from typing import Dict
from re import search

def local_execution_with_others_projects_modules(current_file: str, directory_to_go_back: int = 1) -> Path:
    """
    If in a local execution the current module uses resources from another \n
    module that is in the same project i.e.(this module is in the same \n
    directory as the other module) and they aren't in the same level. \n
    So you need to add the common dictory between the modules to the Paths of execution \n
    
    Args:
        `current_file` (str): file path who calls the other modules

    Returns:
        `Path`: common directory Path
    """
    
    src_folder = Path(current_file.split(":")[0].upper() + ":" + current_file.split(":")[1])
    for _ in range(directory_to_go_back): src_folder = src_folder.parent
    if src_folder.__str__() not in sys.path: sys.path.append(str(src_folder))
    if Path.cwd().__str__() not in sys.path: sys.path.append(Path.cwd().__str__())

    return src_folder

if __name__ == "__main__":
    src_folder = local_execution_with_others_projects_modules(__file__, 4)

from services.logger import create_logger
import csv
from services.zendesk.api_requests import get_ticket_info, get_field_info


if __name__ == '__main__': 
    zen_mapper_loger = create_logger('')
    zen_mapper_loger.setLevel(10)
else: 
    zen_mapper_loger = create_logger(__name__, without_handler = True)


def main(ticket_id: int, file_name: str, env: str = "homo"):

    tickets_book = get_ticket_info(ticket_id, env)
    custom_fields = tickets_book.data["ticket"]["custom_fields"]

    fields_with_custom_options = []
    for field_id_and_value in custom_fields:

        if field_id_and_value["value"] is None: continue

        field_info_referee = get_field_info(field_id_and_value["id"], env)
        if field_info_referee.failure:
            zen_mapper_loger.error("Finalizando aplicação devido a erro")
            exit()
        
        field_info: Dict = field_info_referee.data["ticket_field"]

        item = {
        "id": field_info["id"],
        "title": field_info["title"]
        }
    
        if "custom_field_options" in tuple(field_info.keys()):
            fields_with_custom_options.append(item)

    folder = Path(__file__).parent
    with open(folder / file_name, "w", newline= "\n", encoding= "utf-8") as f_io:

        csv_writer = csv.DictWriter(f_io, fieldnames = item.keys())
        csv_writer.writeheader()
        csv_writer.writerows(fields_with_custom_options)

def is_a_valid_data(field_id: str, data: str, env: str = "prd", default_data: str = "99999"):

    field_info = get_field_info(field_id, env)
    if field_info.failure:
        raise Exception(field_info.error_description)

    regex_pattern: str = field_info.data["ticket_field"]["regexp_for_validation"]
    regex_pattern = regex_pattern.replace("z", 'Z')
    validation = search(regex_pattern, data)

    if validation is None:
        return default_data
    else:
        return data
    
    



if __name__ == "__main__": 

    nsu_1 = 'r06615'
    nsu_2 = '687541.5'
    nsu_3 = '654321978'

    #27259451200397
    x = get_field_info(32096947096973, "homo")

    # for options in x.data["ticket_field"]["custom_field_options"]:

    #     print("x")

    folder = Path(__file__).parent
    with open(folder / "tags_de_um_campo.csv", "w", newline= "\n", encoding= "utf-8") as f_io:

        csv_writer = csv.DictWriter(f_io, fieldnames = x.data["ticket_field"]["custom_field_options"][0].keys())
        csv_writer.writeheader()
        csv_writer.writerows(x.data["ticket_field"]["custom_field_options"])

    z =  is_a_valid_data(32571286805261, nsu_3)
    #regexp_for_validation
    x = get_field_info(27486246039309, "homo") #'^\\d+$'
    y = get_field_info(32571286805261, "homo") #\\A[-+]?\\d+\\z

    ticket_id = 7657
    file_name = "demit.csv"

    main(7720, file_name)



