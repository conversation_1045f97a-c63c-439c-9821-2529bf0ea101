from types import coroutine
import openpyxl
import os 
import xlrd
import csv
import utils

from datetime import datetime
from bs4 import BeautifulSoup

        

class ExcelService():
    
    def __init__(self, config, path_download):

        self.app_root_path = utils.get_application_root_path()
        self.config = config
        self.path_download = path_download

        super().__init__()
    
    def fill_cells_cielo(self, data:list) -> None:
        # """Gera planilha conforme template da CIELO
        # Args:
        #     data (dict): Lista de dicionarios com as informacoes do estorno a ser requisitado
        # """        

        if not len(data): return

        workbook_path = self.app_root_path+self.config['estrutura']['templates']['cielo']
        workbook = openpyxl.load_workbook(workbook_path)    
        offset = 12; index = 0        
        # Ativando a primeira planilha do workbook
        sheet = workbook[workbook.sheetnames[0]]                
        # Data deve ser uma lista de dicionarios
        for info in data:
            # Formando valores
            _valor_compra = str(info['valor_compra']).replace('.', ',')
            _valor_cancel = str(info['valor_cancel']).replace('.', ',')

            sheet['A{}'.format(index+offset)].value = info['estabelecimento']
            sheet['B{}'.format(index+offset)].value = info['lote']
            sheet['C{}'.format(index+offset)].value = info['data_deposito']
            sheet['D{}'.format(index+offset)].value = info['numero_cartao']
            sheet['E{}'.format(index+offset)].value = info['data_venda']
            sheet['F{}'.format(index+offset)].value = _valor_compra
            sheet['G{}'.format(index+offset)].value = _valor_cancel
            sheet['H{}'.format(index+offset)].value = info['codigo_autorizacao']
            sheet['I{}'.format(index+offset)].value = info['motivo']     
            index += 1             
        # Salvando planilha para envio
        
        option = self.path_download

        if option == 0:
            workbook.save('{}\\{}'.format(self.config['firefox'] ['download_path_eccomerce'], 
                                        self.__get_sheet_name('cielo'))) 
        if option == 1:
            workbook.save('{}\\{}'.format(self.config['firefox'] ['download_path_PDV'], 
                                        self.__get_sheet_name('cielo'))) 
        if option == 2:
            workbook.save('{}\\{}'.format(self.config['firefox'] ['download_path_retorno'], 
                                        self.__get_sheet_name('cielo')))
        if option == 96:
            workbook.save('{}\\{}'.format(self.config['firefox'] ['download_path_poslio'], 
                                        self.__get_sheet_name('cielo')))                                


    def fill_cells_rede(self, data:list) -> None:
        """Gera planilha conforme template da REDE
        Args:
            data (dict): Lista de dicionarios com as informacoes do estorno a ser requisitado
        """               
                  
        if not len(data): return

        workbook_path = self.app_root_path+self.config['estrutura']['templates']['rede']
        workbook = openpyxl.load_workbook(workbook_path)        
        offset = 2; index = 0        
        # Ativando a primeira planilha do workbook
        sheet = workbook[workbook.sheetnames[0]]

        # Data deve ser uma lista de dicionarios
        for info in data:
            # Formatando valor da compra
            _valor_compra = str(info['valor_compra']).replace(',', '.')

            sheet['A{}'.format(index+offset)].value = int(info['filial'])
            sheet['B{}'.format(index+offset)].value = info['cnpj_filial']
            sheet['C{}'.format(index+offset)].value = info['data_venda']
            sheet['D{}'.format(index+offset)].value = 'R$ {}'.format(_valor_compra)
            sheet['E{}'.format(index+offset)].value = info['numero_cartao']
            sheet['F{}'.format(index+offset)].value = info['nsu']
            index += 1                  
        workbook.save('{}\\{}'.format(self.config['firefox']['default_download_path'], 
                                        self.__get_sheet_name('rede')))     



    def __get_sheet_name(self, autorizadora:str = None) -> str:
        """Gera o nome da planilha que deve ser enviada para a autorizadora
        Args:
            autorizadora (str, optional): Nome da adquirente. Defaults to None.
        Raises:
            Exception: Error
        Returns:
            str: Nome do arquivo gerado
        """        

        filename = ''
        
        curr_datetime = datetime.today()
        
        day = curr_datetime.day if curr_datetime.day > 10 else '0{}'.format(curr_datetime.day)
        month = curr_datetime.month if curr_datetime.month > 10 else '0{}'.format(curr_datetime.month)
        year = str(curr_datetime.year)[2:]
        hour = curr_datetime.hour if curr_datetime.hour > 10 else '0{}'.format(curr_datetime.hour)
        minu = curr_datetime.minute if curr_datetime.minute > 10 else '0{}'.format(curr_datetime.minute)
        
        if autorizadora.lower() == 'cielo':
            filename = 'CIELO-PAGUEMENOS-{}{}{}-{}{}.xlsx'.format(day, month, year, hour, minu)
        elif autorizadora.lower() == 'rede':
            filename = 'REDE-PAGUEMENOS-{}{}{}-{}{}.xlsx'.format(day, month, year, hour, minu)
        else:
            raise Exception('Error')

        return filename
    
   
    def read_data_retorno_cielo(self, path:str=None):            
        path = self.config['firefox']['download_path_retorno']
        data = []
        for filename in os.listdir(path):            
            index = 0   
            filepath = '{}\\{}'.format(path, filename)
            filepath = self.__convert_file(filepath=filepath, extension='xlsx')
            workbook = openpyxl.load_workbook(filepath)
            sheet = workbook[workbook.sheetnames[0]]
            for cell in sheet.values:            
                if index >= 11:
                    data.append(cell)
                index+=1     
        return data


    # Era usado para converter o arquivo vindo do CTF
    def __convert_file(self, filepath, extension='html'):  
        file_extension = os.path.splitext(filepath)[1]  
        if file_extension != '.'+extension:            
            new_filepath = filepath+file_extension.replace(file_extension, '')+'.'+extension
            os.rename(filepath, new_filepath)
            return new_filepath
        return filepath       

    def report_vtex_manual(self, data:list) -> None:
        filename = 'report_vtex-'+datetime.today().strftime('%d%m%Y')+'.xlsx'
        offset = 2; index = 0
        wb= openpyxl.Workbook()
        ws = wb.active
        ws['A1'] = 'Filial'
        ws['B1'] = 'Sequencial'
        ws['C1'] = 'Valor'
        ws['D1'] = 'Status'
        for venda in data:
            ws['A{}'.format(index+offset)] = venda['filial']
            ws['B{}'.format(index+offset)] = venda['sequencial']
            ws['C{}'.format(index+offset)] = venda['valor_cancelamento']
            ws['D{}'.format(index+offset)] = venda['status']
            index+=1
        utils.FileDirHandler(self.config['firefox']['default_download_path']).create_dir()
        wb.save(self.config['firefox']['default_download_path']+'\\'+filename)

        



