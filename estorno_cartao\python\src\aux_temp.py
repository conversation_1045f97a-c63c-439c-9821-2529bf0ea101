
from services.estorno_service import EstornoService
from services.multi_report_handler import log_report
from typing import Dict, Union
from models.refund_log import *


def register_refund_activity(refund_info_raw: Union[Dict, EstornoLog], estorno_service: EstornoService):

    refund_info = refund_info_raw if isinstance(refund_info_raw, EstornoLog) else turn_estorno_log_to_dataclasse(refund_info_raw)

    msg = refund_info.motivo
    if msg is None: msg = refund_info.status
        
    print(msg)
    estorno_service.record_refund_processment(refund_info)
    log_report(f"Estorno {refund_info.idEstorno}: {msg}")  