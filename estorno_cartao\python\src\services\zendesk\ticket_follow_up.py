from pathlib import Path
import sys
from json import dumps
from typing import Dict
from datetime import datetime, timedelta
from time import sleep
from urllib3.connectionpool import log as urlliblogger

def local_execution_with_others_projects_modules(current_file: str, directory_to_go_back: int = 1) -> Path:
    """
    If in a local execution the current module uses resources from another \n
    module that is in the same project i.e.(this module is in the same \n
    directory as the other module) and they aren't in the same level. \n
    So you need to add the common dictory between the modules to the Paths of execution \n
    
    Args:
        `current_file` (str): file path who calls the other modules

    Returns:
        `Path`: common directory Path
    """
    
    src_folder = Path(current_file.split(":")[0].upper() + ":" + current_file.split(":")[1])
    for _ in range(directory_to_go_back): src_folder = src_folder.parent
    if src_folder.__str__() not in sys.path: sys.path.append(str(src_folder))
    if Path.cwd().__str__() not in sys.path: sys.path.append(Path.cwd().__str__())

    return src_folder

if __name__ == "__main__":
    src_folder = local_execution_with_others_projects_modules(__file__, 3)

from services.logger import create_logger
from services.zendesk.api_requests import get_ticket_info_with_rate_limit, get_audits_for_ticket
from services.zendesk.zen_var import ENDING_STATUS, PENDING, REMAINING_PERCENTAGE_TO_BE_ABOVE_OF, NEW
import Config.vars as global_vars
import services.zendesk.references.map_ids as map_ids
from Connections.search_db import SearchDb
from Connections.update_db import UpdateDb
from models.refund_log import estorno_venda_log_orm

log_level = 20
urlliblogger.setLevel(log_level)
if __name__ == '__main__': 
    ticket_fup_logger = create_logger('')
    ticket_fup_logger.setLevel(log_level)
else: 
    ticket_fup_logger = create_logger(__name__, without_handler = True)

API_REQUEST_LIMIT = 700
MIGRATION_DATE = '08/10/2024 20:00:00'
MIGRATION_DATE_PATTERN = "%d/%m/%Y %H:%M:%S"
WAIT_REQUESTTIME_SECONDS = 62
EXPIRED_TICKET_TAG =  "csc_fechar_ticket_pendente" 

def verify_not_solved_tickets(env: str = "prd"):

    ticket_fup_logger.info("Iniciando verificação de ticket resolvidos")

    finder = SearchDb()
    changer = UpdateDb()

    refund_request_not_solved = []

    duality_stage_tickets, _ = finder.search_not_solved_ticket_in_duality_stage()
    after_migration_ticket, _ = finder.search_not_solved_after_migration(MIGRATION_DATE)

    if duality_stage_tickets is not None: refund_request_not_solved.extend(duality_stage_tickets)
    if after_migration_ticket is not None: refund_request_not_solved.extend(after_migration_ticket)
    

    if len(refund_request_not_solved) == 0:
        ticket_fup_logger.info("Sem ticket não resolvidos")
        return 0

    api_requests = 0
    for refund_request in refund_request_not_solved:

        try:

            refund_request_process = estorno_venda_log_orm(refund_request)

            ticket_fup_logger.info(f"Verificação do ticket do pedido de estorno id_estorno: {refund_request_process.idEstorno}; data_cadastro: {refund_request['DATA_CADASTRO']}")

            api_requests = 1 + api_requests
            ticket_fup_logger.info(f"Contador de requisições: {api_requests}")

            if refund_request["DATA_CADASTRO"] < datetime.strptime(MIGRATION_DATE, MIGRATION_DATE_PATTERN):
                zendesk_id = refund_request["NUMERO_DELIVERY"]
                if zendesk_id is None or zendesk_id == "": zendesk_id = refund_request["ID_INST_BPM"]
            else:
                zendesk_id = refund_request["ID_INST_BPM"]
                
            ticket_res, response_headers = get_ticket_info_with_rate_limit(zendesk_id, env)

            ticket_fup_logger.info(f"número do ticket {zendesk_id}")

            if "error" in ticket_res.data and ticket_res.data["error"] == "RecordNotFound":

                msg = "Ticket foi excluído"
                ticket_fup_logger.error(msg + " " + zendesk_id)

                refund_request_process.status = global_vars.STATUS_EXPIRED_TICKET
                refund_request_process.detalhamento = global_vars.STATUS_EXPIRED_TICKET

                changer.record_ticket_finalization(refund_request_process, datetime.today(), msg)
                continue

            if ticket_res.failure:
                raise Exception(ticket_res.error_description)

            if "error" in ticket_res.data:
                msg = f"Error ao obter as informações do ticket"
                ticket_fup_logger.error(msg)
                raise Exception(msg)

            ratelimit = int(response_headers["ratelimit-limit"][1])
            ratelimit_remaining = int(response_headers["ratelimit-remaining"][1])

            if ratelimit_remaining < (REMAINING_PERCENTAGE_TO_BE_ABOVE_OF * ratelimit): 
                ticket_fup_logger.info(f"Limite de requisições atingido, aguardando {WAIT_REQUESTTIME_SECONDS} segundos")
                sleep(WAIT_REQUESTTIME_SECONDS)

            ticket_info: Dict = ticket_res.data
            # closed
            status = ticket_info["ticket"]["status"] 
        
            if status == PENDING and refund_request_process.status != global_vars.STATUS_PENDING_PHARMACY:
                ticket_fup_logger.info(f"ticket {zendesk_id} está pendente")

                refund_request_process.status = global_vars.STATUS_PENDING_PHARMACY
                refund_request_process.detalhamento = global_vars.PENDING_PHARMACY_OBSERVATION
                changer.change_status_and_observation(refund_request_process)
                continue

            if status not in ENDING_STATUS or status == NEW:
                ticket_fup_logger.info(f"ticket {zendesk_id} ainda não foi resolvido")
                ticket_fup_logger.info("Indo para próximo pedido de estorno")
                continue
                
            #Ajustar fuso horário
            last_update = datetime.strptime(ticket_info["ticket"]["updated_at"], "%Y-%m-%dT%H:%M:%SZ")
            last_update = last_update - timedelta(hours = 3)

            #Verificar se foi um finalização automática
            if EXPIRED_TICKET_TAG in ticket_info["ticket"]["tags"]:

                manual_refund_process_status = global_vars.MANUAL_PROCESSMENT_STATUS_EXPIRED_TICKET
                refund_request_process.status = global_vars.STATUS_EXPIRED_TICKET
                refund_request_process.detalhamento = global_vars.EXPIRED_TICKET_OBSERVATION

            else:
                kind_of_refund = get_kind_of_refund_ticket(ticket_info, env)
                manual_refund_process_status = refund_request_process.status = global_vars.STATUS_REFUND_PROCESS_END
                ticket_audits_info, _ = get_audits_for_ticket(zendesk_id)
                str_audits = dumps(ticket_audits_info.data)

                if global_vars.MANUAL_PROCESSMENT_MACRO_SUCESSFUL_PATTERN in str_audits and "pix" not in kind_of_refund.lower():
                    refund_request_process.status = global_vars.STATUS_REFUND_PROCESS_END
                    refund_request_process.detalhamento = global_vars.REFUND_PROCESS_END_OBSERVATION_CARD
                
                elif global_vars.MANUAL_PROCESSMENT_MACRO_SUCESSFUL_PATTERN in str_audits and "pix" in kind_of_refund.lower():
                    refund_request_process.status = global_vars.STATUS_REFUND_PROCESS_END
                    refund_request_process.detalhamento = global_vars.REFUND_PROCESS_END_OBSERVATION_PIX

                else:
                    ticket_fup_logger.info(f"Não foi identificado macro de conclusão para ticket {zendesk_id}")
                    refund_request_process.status = global_vars.STATUS_EXPIRED_TICKET

                    if "pix" in kind_of_refund.lower():
                        refund_request_process.detalhamento = global_vars.REFUND_PROCESS_END_OBSERVATION_PIX
                    else:
                        refund_request_process.detalhamento = global_vars.REFUND_PROCESS_END_OBSERVATION_CARD
                    
                    

            changer.record_ticket_finalization(refund_request_process, last_update, manual_refund_process_status)
            ticket_fup_logger.info(f"ticket {zendesk_id} resolvido")

        except Exception as e:
            ticket_fup_logger.error(f"Erro ao verificar ticket id_estorno:{refund_request['ID_ESTORNO']}: ")
            ticket_fup_logger.error(f"Detalhes do erro: {e}")
            
    ticket_fup_logger.info("Finalizando verificação de ticket resolvidos")

    print("fim")

def get_kind_of_refund_ticket(ticket_info: Dict, env: str = "prd") -> str:
    """Obter o tipo de cancelamento do ticket

    Args:
        ticket_info (Dict): informações do ticket
        env (str, optional): ambiente. Defaults to "prd".

    Returns:
        str: tipo de cancelamento
    """
    kind_of_refund_ticket_field_id = map_ids.COMMON_FIELD_ID_MAPPING[env].get("cancelamento")
    for custom_field in ticket_info["ticket"]["custom_fields"]:
        if custom_field["id"] == kind_of_refund_ticket_field_id:
            kind_of_refund = custom_field["value"]
            return kind_of_refund
    
        

if __name__ == "__main__":

    # 366348 cartão prd
    # 366169 pix prd
    # solved 
    # new 
    env = "prd" 
    ticket_id = 401695

    verify_not_solved_tickets(env)

    print("fim")



