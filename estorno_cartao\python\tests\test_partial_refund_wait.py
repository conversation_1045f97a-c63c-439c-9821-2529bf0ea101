import pytest
from unittest.mock import patch, MagicMock
from datetime import datetime
import sys
from pathlib import Path

src_path = Path(__file__).parent.parent
sys.path.append(str(src_path))

from src.utils import is_there_a_partial_refund_to_wait
from src.models.result_handle import ResultHandle


class TestPartialRefundWait:
    
    @pytest.fixture
    def mock_current_refund(self):
        return {
            'codigoFilialOrigem': 123,
            'codigoAutorizacao': 'AUTH123'
        }
    
    @patch('utils.SearchDb')
    @patch('utils.datetime')
    def test_no_existing_refunds(self, mock_datetime, mock_search_db, mock_current_refund):
        # Configuração
        mock_finder = MagicMock()
        mock_search_db.return_value = mock_finder

        # Simula a função de busca retornando dados vazios (nenhum estorno existente)
        mock_finder.is_there_already_a_refund_for_the_same_transaction.return_value = ResultHandle.Ok([])

        # Execução
        result = is_there_a_partial_refund_to_wait(mock_current_refund)

        # Verificação
        assert result is False
        mock_finder.is_there_already_a_refund_for_the_same_transaction.assert_called_once_with(
            mock_current_refund['codigoFilialOrigem'],
            mock_current_refund['codigoAutorizacao']
        )
    
    @patch('utils.SearchDb')
    @patch('utils.datetime')
    def test_search_failure(self, mock_datetime, mock_search_db, mock_current_refund):
        # Configuração
        mock_finder = MagicMock()
        mock_search_db.return_value = mock_finder

        # Simula a função de busca retornando falha
        mock_finder.is_there_already_a_refund_for_the_same_transaction.return_value = ResultHandle.Fail("Erro no banco de dados")

        # Execução
        result = is_there_a_partial_refund_to_wait(mock_current_refund)

        # Verificação
        assert result is True  # Deve retornar True em caso de falha
    
    @patch('utils.SearchDb')
    @patch('utils.datetime')
    def test_recent_refund_less_than_one_day(self, mock_datetime, mock_search_db, mock_current_refund):
        # Configuração
        mock_finder = MagicMock()
        mock_search_db.return_value = mock_finder

        # Define a data atual
        current_date = datetime(2025, 6, 10, 13, 0, 0)
        mock_datetime.today.return_value = current_date

        # Simula a função de busca retornando um estorno recente (menos de 1 dia)
        refund_date = datetime(2025, 6, 10, 12, 30, 15)  # Apenas 30 minutos atrás
        mock_finder.is_there_already_a_refund_for_the_same_transaction.return_value = ResultHandle.Ok([
            {'DATA_DEVOLUCAO': refund_date}
        ])

        # Execução
        result = is_there_a_partial_refund_to_wait(mock_current_refund)

        # Verificação
        assert result is True  # Deve aguardar por estorno recente
    
    @patch('utils.SearchDb')
    @patch('utils.datetime')
    def test_refund_more_than_one_day(self, mock_datetime, mock_search_db, mock_current_refund):
        # Configuração
        mock_finder = MagicMock()
        mock_search_db.return_value = mock_finder

        # Define a data atual
        current_date = datetime(2025, 6, 11, 13, 0, 0)
        mock_datetime.today.return_value = current_date

        # Simula a função de busca retornando um estorno antigo (mais de 1 dia)
        refund_date = datetime(2025, 6, 9, 12, 3, 15)  # Mais de 1 dia atrás
        mock_finder.is_there_already_a_refund_for_the_same_transaction.return_value = ResultHandle.Ok([
            {'DATA_DEVOLUCAO': refund_date}
        ])

        # Execução
        result = is_there_a_partial_refund_to_wait(mock_current_refund)

        # Verificação
        assert result is False  # Não deve aguardar por estorno antigo

if __name__ == "__main__":
    pytest.main([__file__])