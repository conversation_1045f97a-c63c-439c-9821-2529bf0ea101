

KEY_BOTCITY = "PME_PDDJFSYKUJRJ405QLZ2L"
LOGIN_BOTCITY = "pmenos"
SERVER_BOTCITY = "https://paguemenos.botcity.dev/"

TOKEN_REFUND_SCOPE = "api-estorno"
KEY_FOR_REFUND_ID = "reversal_request_id"



CREDS_CREDSHOP_KEYS_STRUCTURE = {
    "client_id": "client_id",
    "client_secret": "client_secret",
    "refund_token": "refund_token"
}


CREDS_CREDSHOP = {
    "stg": {
        "label":"credshop_stg",
        "keys_structure": CREDS_CREDSHOP_KEYS_STRUCTURE,
        "token_url": "https://accounts-hml.credishop.com.br/oauth/token",
        "refund_url": "https://ajuste-hml.credishop.com.br/api/v1/reversal",
        "track_refund_url":"https://ajuste-hml.credishop.com.br/api/v1/reversal/{refund_id}"
    },
    "prd": {
        "label":"credshop_prd",
        "keys_structure": CREDS_CREDSHOP_KEYS_STRUCTURE,
        "token_url": "https://accounts.credishop.com.br/oauth/token",
        "refund_url": "https://ajuste.credishop.com.br/api/v1/reversal",
        "track_refund_url":"https://ajuste.credishop.com.br/api/v1/reversal/{refund_id}"
    }
}


NOT_A_MAPPED_ERROR_MSG = "Erro não mapeado ao pedir estorno credi-shop"

