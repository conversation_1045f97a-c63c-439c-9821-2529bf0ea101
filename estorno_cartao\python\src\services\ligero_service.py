import requests
from datetime import datetime

class LigeroHandler():

    def __init__(self, constants: dict, username: str = None, password: str = None):        
        self.constants = constants
        self.username = constants['username'] if not bool(username) else username
        self.password = constants['password'] if not bool(password) else password
        self.base_url = constants['base_url']
        self.process_dynamic_fields = constants['process_dynamic_fields']
        self.state_ids = constants['state_ids']
        self.service_id = constants['service_id']
        self.queue_id = constants['queue_ids']        
        self.verify = False
        super().__init__()

    def login(self) -> (str):
        print("LigeroHandler - login()")
        _url = self.base_url.format(method='Login')
        request_body = {'UserLogin': self.username, 'Password': self.password}
        response = requests.post(url=_url, json=request_body, verify=self.verify)
        if not (response.ok and self.__is_ok(response.json())):
            raise Exception('Error: {}'.format(response.text))

        return (response.json()['SessionID'], response.json()['UserID'])

    # def list_tickets(*args, **kwargs)

    def list_tickets_null(self, session_id: str, channel:str, state:str) -> (list):
        print("LigeroHandler - list_tickets_null()")
        print("Session_ID: ", session_id)
        service_id = self.service_id
        state = self.constants['state_ids'][state]       
        ticket_date =  datetime.today().strftime('%Y-%m-%d %H:%M:%S')
        print("Ticket_date: ",ticket_date)

        _url = self.base_url.format(method='TicketList')
        print("URL: ", _url)
        state_id = state
        print("State_ID: ", state_id)

        request_body = {
            "SessionID": session_id,
            "ServiceIDs": [service_id],
            "TicketCreateTimeOlderDate": ticket_date,
            "StateIDs": [state_id],
            "DynamicField_TIPOVENDA": {
                "Empty": 1
	        },
            "Limit": 2000,
        }  if channel == 'eco' else {
            "SessionID": session_id,
            "ServiceIDs": [service_id],
            "TicketCreateTimeOlderDate": ticket_date,
            "StateIDs": [state_id],
            "DynamicField_TIPOVENDA": {
                "Empty": 1
            },
            "Limit": 2000,
        }

        response = requests.post(url=_url, json=request_body, verify=self.verify)

        if not (response.ok and self.__is_ok(response.json())):
            raise Exception('Error: {}'.format(response.text))

        return self.__extract_ticket_ids(tickets=response.json())


    def list_tickets(self, session_id: str, channel:str, state:str) -> (list):
        print("LigeroHandler - list_tickets()")
        print("Session_ID: ", session_id)
        service_id = self.service_id
        state = self.constants['state_ids'][state]  
             
        ticket_date =  datetime.today().strftime('%Y-%m-%d %H:%M:%S')
        print("Ticket_date: ",ticket_date)

        _url = self.base_url.format(method='TicketList')
        print("URL: ", _url)
        state_id = state
        
        print("State_ID: ", state_id)
       
        
        if channel == 'eco':
            request_body = {
                "SessionID": session_id,
                "ServiceIDs": [service_id],
                "QueueIDS":[17,131,135,136],#CSC - Admin 1 = 135,CSC - Admin 2 = 136,G_Dir Administrativa - CSC Administrativo = 131,Contas a Receber Cartões = 17
                "TicketCreateTimeOlderDate": ticket_date,
                "StateIDs": [1,13],#(chamados novos id 1, chamados pendentes id 13)
                "DynamicField_TIPOVENDA": {
                    "Equals": ["e-Commerce", "Clique e Retire"]
                },
                "Limit": 2000,
            }  
        
        elif channel == 'pdv':    
             request_body = {
                "SessionID": session_id,
                "ServiceIDs": [service_id],
                "QueueIDS":[17,131,135,136],#CSC - Admin 1 = 135,CSC - Admin 2 = 136,G_Dir Administrativa - CSC Administrativo = 131,Contas a Receber Cartões = 17
                "TicketCreateTimeOlderDate": ticket_date,
                "StateIDs": [1,13],#(chamados novos id 1, chamados pendentes id 13)
                "DynamicField_TIPOVENDA": {
                    "Equals": ["ECF"]
                },
                "Limit": 2000,
            }
        
        elif channel == 'pos':    
             request_body = {
                "SessionID": session_id,
                "ServiceIDs": [service_id],
                "QueueIDS":[17,131,135,136],#CSC - Admin 1 = 135,CSC - Admin 2 = 136,G_Dir Administrativa - CSC Administrativo = 131,Contas a Receber Cartões = 17
                "TicketCreateTimeOlderDate": ticket_date,
                "StateIDs": [1,13],#(chamados novos id 1, chamados pendentes id 13)
                "DynamicField_TIPOVENDA": {
                    "Equals": ["POS/LIO"]
                },
                "Limit": 2000,
            }        
                                   
        response = requests.post(url=_url, json=request_body, verify=self.verify)

        if not (response.ok and self.__is_ok(response.json())):
            raise Exception('Error: {}'.format(response.text))

        return self.__extract_ticket_ids(tickets=response.json())

    def ticket_details(self, session_id: str, ticket_id: int) -> (list):
        print("LigeroHandler - ticket_details()")
        _url = self.base_url.format(method='TicketGet')
        request_body = {
            "SessionID": session_id,
            "TicketID": ticket_id,
            "DynamicFields": 1
        }
        response = requests.post(url=_url, json=request_body, verify=self.verify)
        if not (response.ok and self.__is_ok(response.json())):
            raise Exception('Error: {}'.format(response.text))
        return self.__extract_dynamic_fields(response.json())

    def update_ticket_status(self, session_id: str, ticket_id: int, state:str=None, instancia:str=None) -> (dict):
        print("LigeroHandler - update_ticket_status()")
        _url = self.base_url.format(method='TicketUpdate')
        if state == 'resolvido':
            return {}
        
        request_body = {
            'SessionID': session_id,
            'TicketID': ticket_id,
            'Article': self.constants['articles'][state],
            'Ticket': {
                'StateID': self.constants['state_ids'][state], 
                'LockID': 1, 
                'PendingTime':self.__build_pending_time_obj()
            }
        } if state != 'encerrado' else {
            'SessionID': session_id,
            'TicketID': ticket_id,            
            'Ticket': {
                'StateID': self.constants['state_ids'][state], 
                'LockID': 1
            }
        }

        
        body = self.constants['articles']['pendente']['Body']
    
        if (state == 'pendente' and instancia != None): 
            request_body['Article']['Body'] = self.constants['articles']['pendente']['Body'].format(instancia)
             
        
        print(request_body)
        #input('breackPoint')
        response = requests.post(url=_url, json=request_body, verify=self.verify)

        if state == 'pendente' and instancia != None:
            request_body['Article']['Body'] = body

        if not (response.ok and self.__is_ok(response.json())):
            raise Exception('Error: {}'.format(response.text))
        return response.json()

    def __is_ok(self, response: dict):
        if 'Error' in response:
            return False
        return True

    def __extract_ticket_ids(self, tickets: dict) -> (tuple):
        _tickets = tickets['Tickets'] if bool(tickets['Tickets']) else []
        ids = []
        for ticket in _tickets:
            ids.append((int(ticket['TicketID']), ticket['TicketNumber']))
        return ids

    def __extract_dynamic_fields(self, ticket_details: str, process_dynamic_fields: list = None) -> (list):

        _process_dynamic_fields = self.process_dynamic_fields if not bool(
            process_dynamic_fields) else process_dynamic_fields

        dynamic_fields = {}
        check_fields = len(_process_dynamic_fields)
        found_fields = 0

        for dynamic_field in ticket_details['Ticket'][0]['DynamicField']:
            if dynamic_field['Name'] in _process_dynamic_fields:
                dynamic_fields[dynamic_field['Name']] = dynamic_field['Value']
                found_fields += 1
                if check_fields == found_fields:
                    break

        return dynamic_fields
    
    def __build_pending_time_obj(self, date:str=None) -> (dict):
        date = datetime.today()
        pending_time = {
            'Year': date.year, 'Month': date.month,
            'Day': date.day, 'Hour': date.hour,
            'Minute': date.minute
        }
        return pending_time


  

    
