<?xml version="1.0" encoding="UTF-8"?>
<job>
  <name>job_pdv_erro</name>
  <description/>
  <extended_description/>
  <job_version/>
  <job_status>0</job_status>
  <directory>&#x2f;</directory>
  <created_user>-</created_user>
  <created_date>2021&#x2f;02&#x2f;04 08&#x3a;52&#x3a;19.385</created_date>
  <modified_user>-</modified_user>
  <modified_date>2021&#x2f;02&#x2f;04 08&#x3a;52&#x3a;19.385</modified_date>
    <parameters>
    </parameters>
    <slaveservers>
    </slaveservers>
      <job-log-table>
        <connection/>
        <schema/>
        <table/>
        <size_limit_lines/>
        <interval/>
        <timeout_days/>
        <field>
          <id>ID_JOB</id>
          <enabled>Y</enabled>
          <name>ID_JOB</name>
        </field>
        <field>
          <id>CHANNEL_ID</id>
          <enabled>Y</enabled>
          <name>CHANNEL_ID</name>
        </field>
        <field>
          <id>JOBNAME</id>
          <enabled>Y</enabled>
          <name>JOBNAME</name>
        </field>
        <field>
          <id>STATUS</id>
          <enabled>Y</enabled>
          <name>STATUS</name>
        </field>
        <field>
          <id>LINES_READ</id>
          <enabled>Y</enabled>
          <name>LINES_READ</name>
        </field>
        <field>
          <id>LINES_WRITTEN</id>
          <enabled>Y</enabled>
          <name>LINES_WRITTEN</name>
        </field>
        <field>
          <id>LINES_UPDATED</id>
          <enabled>Y</enabled>
          <name>LINES_UPDATED</name>
        </field>
        <field>
          <id>LINES_INPUT</id>
          <enabled>Y</enabled>
          <name>LINES_INPUT</name>
        </field>
        <field>
          <id>LINES_OUTPUT</id>
          <enabled>Y</enabled>
          <name>LINES_OUTPUT</name>
        </field>
        <field>
          <id>LINES_REJECTED</id>
          <enabled>Y</enabled>
          <name>LINES_REJECTED</name>
        </field>
        <field>
          <id>ERRORS</id>
          <enabled>Y</enabled>
          <name>ERRORS</name>
        </field>
        <field>
          <id>STARTDATE</id>
          <enabled>Y</enabled>
          <name>STARTDATE</name>
        </field>
        <field>
          <id>ENDDATE</id>
          <enabled>Y</enabled>
          <name>ENDDATE</name>
        </field>
        <field>
          <id>LOGDATE</id>
          <enabled>Y</enabled>
          <name>LOGDATE</name>
        </field>
        <field>
          <id>DEPDATE</id>
          <enabled>Y</enabled>
          <name>DEPDATE</name>
        </field>
        <field>
          <id>REPLAYDATE</id>
          <enabled>Y</enabled>
          <name>REPLAYDATE</name>
        </field>
        <field>
          <id>LOG_FIELD</id>
          <enabled>Y</enabled>
          <name>LOG_FIELD</name>
        </field>
        <field>
          <id>EXECUTING_SERVER</id>
          <enabled>N</enabled>
          <name>EXECUTING_SERVER</name>
        </field>
        <field>
          <id>EXECUTING_USER</id>
          <enabled>N</enabled>
          <name>EXECUTING_USER</name>
        </field>
        <field>
          <id>START_JOB_ENTRY</id>
          <enabled>N</enabled>
          <name>START_JOB_ENTRY</name>
        </field>
        <field>
          <id>CLIENT</id>
          <enabled>N</enabled>
          <name>CLIENT</name>
        </field>
      </job-log-table>
      <jobentry-log-table>
        <connection/>
        <schema/>
        <table/>
        <timeout_days/>
        <field>
          <id>ID_BATCH</id>
          <enabled>Y</enabled>
          <name>ID_BATCH</name>
        </field>
        <field>
          <id>CHANNEL_ID</id>
          <enabled>Y</enabled>
          <name>CHANNEL_ID</name>
        </field>
        <field>
          <id>LOG_DATE</id>
          <enabled>Y</enabled>
          <name>LOG_DATE</name>
        </field>
        <field>
          <id>JOBNAME</id>
          <enabled>Y</enabled>
          <name>TRANSNAME</name>
        </field>
        <field>
          <id>JOBENTRYNAME</id>
          <enabled>Y</enabled>
          <name>STEPNAME</name>
        </field>
        <field>
          <id>LINES_READ</id>
          <enabled>Y</enabled>
          <name>LINES_READ</name>
        </field>
        <field>
          <id>LINES_WRITTEN</id>
          <enabled>Y</enabled>
          <name>LINES_WRITTEN</name>
        </field>
        <field>
          <id>LINES_UPDATED</id>
          <enabled>Y</enabled>
          <name>LINES_UPDATED</name>
        </field>
        <field>
          <id>LINES_INPUT</id>
          <enabled>Y</enabled>
          <name>LINES_INPUT</name>
        </field>
        <field>
          <id>LINES_OUTPUT</id>
          <enabled>Y</enabled>
          <name>LINES_OUTPUT</name>
        </field>
        <field>
          <id>LINES_REJECTED</id>
          <enabled>Y</enabled>
          <name>LINES_REJECTED</name>
        </field>
        <field>
          <id>ERRORS</id>
          <enabled>Y</enabled>
          <name>ERRORS</name>
        </field>
        <field>
          <id>RESULT</id>
          <enabled>Y</enabled>
          <name>RESULT</name>
        </field>
        <field>
          <id>NR_RESULT_ROWS</id>
          <enabled>Y</enabled>
          <name>NR_RESULT_ROWS</name>
        </field>
        <field>
          <id>NR_RESULT_FILES</id>
          <enabled>Y</enabled>
          <name>NR_RESULT_FILES</name>
        </field>
        <field>
          <id>LOG_FIELD</id>
          <enabled>N</enabled>
          <name>LOG_FIELD</name>
        </field>
        <field>
          <id>COPY_NR</id>
          <enabled>N</enabled>
          <name>COPY_NR</name>
        </field>
      </jobentry-log-table>
      <channel-log-table>
        <connection/>
        <schema/>
        <table/>
        <timeout_days/>
        <field>
          <id>ID_BATCH</id>
          <enabled>Y</enabled>
          <name>ID_BATCH</name>
        </field>
        <field>
          <id>CHANNEL_ID</id>
          <enabled>Y</enabled>
          <name>CHANNEL_ID</name>
        </field>
        <field>
          <id>LOG_DATE</id>
          <enabled>Y</enabled>
          <name>LOG_DATE</name>
        </field>
        <field>
          <id>LOGGING_OBJECT_TYPE</id>
          <enabled>Y</enabled>
          <name>LOGGING_OBJECT_TYPE</name>
        </field>
        <field>
          <id>OBJECT_NAME</id>
          <enabled>Y</enabled>
          <name>OBJECT_NAME</name>
        </field>
        <field>
          <id>OBJECT_COPY</id>
          <enabled>Y</enabled>
          <name>OBJECT_COPY</name>
        </field>
        <field>
          <id>REPOSITORY_DIRECTORY</id>
          <enabled>Y</enabled>
          <name>REPOSITORY_DIRECTORY</name>
        </field>
        <field>
          <id>FILENAME</id>
          <enabled>Y</enabled>
          <name>FILENAME</name>
        </field>
        <field>
          <id>OBJECT_ID</id>
          <enabled>Y</enabled>
          <name>OBJECT_ID</name>
        </field>
        <field>
          <id>OBJECT_REVISION</id>
          <enabled>Y</enabled>
          <name>OBJECT_REVISION</name>
        </field>
        <field>
          <id>PARENT_CHANNEL_ID</id>
          <enabled>Y</enabled>
          <name>PARENT_CHANNEL_ID</name>
        </field>
        <field>
          <id>ROOT_CHANNEL_ID</id>
          <enabled>Y</enabled>
          <name>ROOT_CHANNEL_ID</name>
        </field>
      </channel-log-table>
   <pass_batchid>N</pass_batchid>
   <shared_objects_file/>
  <entries>
    <entry>
      <name>input_csv</name>
      <description/>
      <type>TRANS</type>
      <specification_method>filename</specification_method>
      <trans_object_id/>
      <filename>&#x24;&#x7b;Internal.Job.Filename.Directory&#x7d;&#x2f;error_msg_eco.ktr</filename>
      <transname/>
      <arg_from_previous>N</arg_from_previous>
      <params_from_previous>N</params_from_previous>
      <exec_per_row>N</exec_per_row>
      <clear_rows>N</clear_rows>
      <clear_files>N</clear_files>
      <set_logfile>N</set_logfile>
      <logfile/>
      <logext/>
      <add_date>N</add_date>
      <add_time>N</add_time>
      <loglevel>Basic</loglevel>
      <cluster>N</cluster>
      <slave_server_name/>
      <set_append_logfile>N</set_append_logfile>
      <wait_until_finished>Y</wait_until_finished>
      <follow_abort_remote>N</follow_abort_remote>
      <create_parent_folder>N</create_parent_folder>
      <logging_remote_work>N</logging_remote_work>
      <parameters>
        <pass_all_parameters>Y</pass_all_parameters>
      </parameters>
      <parallel>N</parallel>
      <draw>Y</draw>
      <nr>0</nr>
      <xloc>400</xloc>
      <yloc>272</yloc>
    </entry>
    <entry>
      <name>TELEGRAM API</name>
      <description/>
      <type>WEBSERVICE_AVAILABLE</type>
      <url>https&#x3a;&#x2f;&#x2f;api.telegram.org&#x2f;bot&#x24;&#x7b;bot_id&#x7d;&#x2f;sendMessage&#x3f;chat_id&#x3d;&#x24;&#x7b;chat_id&#x7d;&#x26;text&#x3d;&#x24;&#x7b;var_message&#x7d;  &#x24;&#x7b;OUTPUT&#x7d;</url>
      <connectTimeOut>0</connectTimeOut>
      <readTimeOut>0</readTimeOut>
      <parallel>N</parallel>
      <draw>Y</draw>
      <nr>0</nr>
      <xloc>656</xloc>
      <yloc>272</yloc>
    </entry>
    <entry>
      <name>Set variables</name>
      <description/>
      <type>SET_VARIABLES</type>
      <replacevars>Y</replacevars>
      <filename/>
      <file_variable_type>JVM</file_variable_type>
      <fields>
        <field>
          <variable_name>bot_id</variable_name>
          <variable_value>1501445650&#x3a;AAEi-vHh7YW0cgb3DWxiI6Awr3OYc7dg9Fw</variable_value>
          <variable_type>JVM</variable_type>
        </field>
        <field>
          <variable_name>chat_id</variable_name>
          <variable_value>-411545323</variable_value>
          <variable_type>JVM</variable_type>
        </field>
        <field>
          <variable_name>var_message</variable_name>
          <variable_value>ESTORNO VENDA - ERRO PDV&#x3a;</variable_value>
          <variable_type>JVM</variable_type>
        </field>
      </fields>
      <parallel>N</parallel>
      <draw>Y</draw>
      <nr>0</nr>
      <xloc>528</xloc>
      <yloc>272</yloc>
    </entry>
    <entry>
      <name>START</name>
      <description/>
      <type>SPECIAL</type>
      <start>Y</start>
      <dummy>N</dummy>
      <repeat>N</repeat>
      <schedulerType>0</schedulerType>
      <intervalSeconds>0</intervalSeconds>
      <intervalMinutes>60</intervalMinutes>
      <hour>12</hour>
      <minutes>0</minutes>
      <weekDay>1</weekDay>
      <DayOfMonth>1</DayOfMonth>
      <parallel>N</parallel>
      <draw>Y</draw>
      <nr>0</nr>
      <xloc>272</xloc>
      <yloc>272</yloc>
    </entry>
  </entries>
  <hops>
    <hop>
      <from>START</from>
      <to>input_csv</to>
      <from_nr>0</from_nr>
      <to_nr>0</to_nr>
      <enabled>Y</enabled>
      <evaluation>Y</evaluation>
      <unconditional>Y</unconditional>
    </hop>
    <hop>
      <from>input_csv</from>
      <to>Set variables</to>
      <from_nr>0</from_nr>
      <to_nr>0</to_nr>
      <enabled>Y</enabled>
      <evaluation>Y</evaluation>
      <unconditional>N</unconditional>
    </hop>
    <hop>
      <from>Set variables</from>
      <to>TELEGRAM API</to>
      <from_nr>0</from_nr>
      <to_nr>0</to_nr>
      <enabled>Y</enabled>
      <evaluation>Y</evaluation>
      <unconditional>N</unconditional>
    </hop>
  </hops>
  <notepads>
  </notepads>
</job>
