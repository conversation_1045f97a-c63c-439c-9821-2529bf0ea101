import requests
import json
import time

class MovimentoService():

    def __init__(self, config:dict):                             
        self.__api_base_url = config['api']['movimento-bpm']['api_base_url']
        self.__data_corte = config['api']['movimento-bpm']['data_corte_busca']
        self.__resource = 'MovimentoControllerV3'         
        super().__init__()       

    def get_vendas_pdv_tef(self) -> list: 
        _query = '/?data={}&plataforma={}&tipo=TEF'.format(self.__data_corte, 'tef')          
        _url = self.__api_base_url.format(self.__resource+_query)   
        print('aaaa ' + _url)
        response = requests.get(_url)        
        print(response)
        if not response.ok: 
            raise ValueError('Erro na requisicao: status {}'.format(response.status_code))        
        response_list = response.json()
        return response_list

    def get_vendas_pdv_tef_extrafarma(self) -> list: 
        _query = '/?data={}&plataforma={}&tipo=TEF'.format(self.__data_corte, 'extef')          
        _url = self.__api_base_url.format(self.__resource+_query)   
        print('aaaa ' + _url)
        response = requests.get(_url)        
        print(response)
        if not response.ok: 
            raise ValueError('Erro na requisicao: status {}'.format(response.status_code))        
        response_list = response.json()
        return response_list    

    def get_vendas_pos_extrafarma(self) -> list: 
        _query = '/?data={}&plataforma={}&tipo=POS'.format(self.__data_corte, 'expos')          
        _url = self.__api_base_url.format(self.__resource+_query)   
        print('aaaa ' + _url)
        response = requests.get(_url)        
        print(response)
        if not response.ok: 
            raise ValueError('Erro na requisicao: status {}'.format(response.status_code))        
        response_list = response.json()
        return response_list    

    def get_vendas_pdv_pos(self) -> list: 
        _query = '/?data={}&plataforma={}&tipo=POS'.format(self.__data_corte, 'tef')  
        _url = self.__api_base_url.format(self.__resource+_query)   
        response = requests.get(_url)     

        if not response.ok: 
            raise ValueError('Erro na requisicao: status {}'.format(response.status_code))        
        response_list = response.json()
        return response_list      
    
    def get_vendas_ecommerce(self) -> list:
        print("MovimentoService - get_vendas_pos()")
        
        _query = '/?data={}&plataforma={}'.format(self.__data_corte, 'pos')  
        _url = self.__api_base_url.format(self.__resource+_query)
        print("URL: ", _url)
        response = requests.get(_url)
        
        print("Response: ", response.json())
        if not response.ok: 
            raise ValueError('Erro na requisicao: status {}'.format(response.status_code))      
        response_list = response.json()
        return response_list