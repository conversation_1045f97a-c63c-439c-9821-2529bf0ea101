from typing import Dict, <PERSON><PERSON>, Union
from pathlib import Path
import sys


def local_execution_with_others_projects_modules(current_file: str, directory_to_go_back: int = 1) -> Path:
    """
    If in a local execution the current module uses resources from another \n
    module that is in the same project i.e.(this module is in the same \n
    directory as the other module) and they aren't in the same level. \n
    So you need to add the common dictory between the modules to the Paths of execution \n
    
    Args:
        `current_file` (str): file path who calls the other modules

    Returns:
        `Path`: common directory Path
    """
    
    src_folder = Path(current_file.split(":")[0].upper() + ":" + current_file.split(":")[1])
    for _ in range(directory_to_go_back): src_folder = src_folder.parent
    if src_folder.__str__() not in sys.path: sys.path.append(str(src_folder))
    if Path.cwd().__str__() not in sys.path: sys.path.append(Path.cwd().__str__())

    return src_folder

if __name__ == "__main__":
    src_folder = local_execution_with_others_projects_modules(__file__, 3)

from services.pagaleve.paga_leve_api import get_checkout, get_payment, request_refund, has_sale_been_fully_refunded
from services.logger import create_logger
from models.refund_log import  EstornoLog, turn_estorno_log_to_dataclasse
import Config.vars as vars
import utils


if __name__ == '__main__': 
    pglev_service_logger = create_logger('')
    pglev_service_logger.setLevel(10)
else: 
    pglev_service_logger = create_logger(__name__, without_handler = True)
    

def process_pagaleve_refund(chekout_id: str, 
                            refund_info_raw: Union[Dict, EstornoLog]) -> Tuple[utils.ResultHandle, EstornoLog]:
    """Realizar estorno de uma compra da Pagaleve

    Args:
        chekout_id (str): ID da sessão de compra na pagaleve
        refund_info_raw (Union[Dict, EstornoLog]): Informações sobre o processamento estorno

    Returns:
        Tuple[utils.ResultHandle, EstornoLog]: Resultado do pedido de estorno, informações sobre processamento de estorno
    """

    pglev_service_logger.info("Iniciando pedido de estorno na pagaleve")

    refund_info = refund_info_raw if isinstance(refund_info_raw, EstornoLog) else turn_estorno_log_to_dataclasse(refund_info_raw)

    enterprise_code = utils.enterprise_code_of_the_branch(refund_info.codigoFilialOrigem)

    get_checkout_info = get_checkout(chekout_id, enterprise_code)
    if get_checkout_info.failure: return treat_refund_result(get_checkout_info, refund_info)

    get_payment_info = get_payment(get_checkout_info.data["payment_id"], enterprise_code)
    if get_payment_info.failure: return treat_refund_result(get_payment_info, refund_info)

    is_fully_refunded = has_sale_been_fully_refunded(get_payment_info.data)
    if is_fully_refunded.success: return treat_refund_result(is_fully_refunded, refund_info)


    refund_request_info = request_refund(get_payment_info.data, refund_info, enterprise_code)
    if refund_request_info.failure: return treat_refund_result(refund_request_info, refund_info)

    return treat_refund_result(refund_request_info, refund_info)



def treat_refund_result(result: utils.ResultHandle, 
            refund_info: EstornoLog) -> Tuple[utils.ResultHandle, EstornoLog]:
        """Tratar resultado do estorno

        Args:
            result (ResultHandle): Resultado do pedido de estorno
            refund_info (EstornoLog): Infomações de estorno no grupo pmenos

        Returns:
            Tuple[ResultHandle, EstornoLog]:
        """

        if result.success and result.success_description is None:

            refund_info.status = vars.STATUS_REFUND_PROCESS_END
            refund_info.motivo = vars.SUCCESSFUL_REFUND_REASON
            refund_info.codigoAutorizacao = result.data["id"]
            return result, refund_info
        
        elif result.success and result.success_description == vars.PAYMENT_METHOD_FULLY_REFUNDED_REASON:

            refund_info.status = vars.STATUS_REFUND_PROCESS_END
            refund_info.motivo = vars.PAYMENT_METHOD_FULLY_REFUNDED_REASON
            return result, refund_info
        
        else:

            refund_info.status = vars.STATUS_PENDENCY_FOR_CSC
            refund_info.motivo = result.error_description
            return result, refund_info
        
