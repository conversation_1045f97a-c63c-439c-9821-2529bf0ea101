from pathlib import Path
import sys
from dataclasses import asdict

def local_execution_with_others_projects_modules(current_file: str, directory_to_go_back: int = 1) -> Path:
    """
    If in a local execution the current module uses resources from another \n
    module that is in the same project i.e.(this module is in the same \n
    directory as the other module) and they aren't in the same level. \n
    So you need to add the common dictory between the modules to the Paths of execution \n
    
    Args:
        `current_file` (str): file path who calls the other modules

    Returns:
        `Path`: common directory Path
    """
    
    src_folder = Path(current_file.split(":")[0].upper() + ":" + current_file.split(":")[1])
    for _ in range(directory_to_go_back): src_folder = src_folder.parent
    if src_folder.__str__() not in sys.path: sys.path.append(str(src_folder))
    if Path.cwd().__str__() not in sys.path: sys.path.append(Path.cwd().__str__())

    return src_folder


src_folder = local_execution_with_others_projects_modules(__file__, 3)

from models.refund_log import estorno_venda_log_orm
from services.logger import create_logger
from Config.database import query_all_db
import Config.vars as global_vars
from models.refund_log import EstornoLog
from services.multi_report_handler import *
from services.vtex_api_service import VTEXHandler
import utils


if __name__ == '__main__': 
    cielo_utils_logger = create_logger('')
    cielo_utils_logger.setLevel(10)
else: 
    cielo_utils_logger = create_logger(__name__, without_handler = True)


def track_cielo_refund_request()-> global_vars.ResultHandle:
    """Quais são os pedidos de estorno em andamento na Cielo

    Returns:
        global_vars.ResultHandle: quando sucesso List[EstornoLog]
    """    

    TRACK_CIELO_REFUND_REQUEST = """
                    SELECT *
                    FROM CosmosRPA.dbo.ESTORNO_VENDA_LOG ev_log (nolock)
                    WHERE 
                    ev_log.STATUS like '%CIELO%'
                    ORDER BY DATA_CADASTRO ASC
                    """

    try:
        cielo_refund_requests = query_all_db(TRACK_CIELO_REFUND_REQUEST)
        result = list(map(estorno_venda_log_orm, cielo_refund_requests))

    except Exception as e:
        msg = "Erro ao buscar estorno em andamento da Cielo"
        cielo_utils_logger.error(msg)
        cielo_utils_logger.error(f"Detalhes do erro: {e}")
        return global_vars.ResultHandle.Fail(f"{msg} | {e}")

    if len(cielo_refund_requests) == 0:
        msg = "Não foi encontrado nenhum estorno em andamento na Cielo"
        cielo_utils_logger.info(msg)
        return global_vars.ResultHandle.Waiting(error=msg)

    
    return global_vars.ResultHandle.Ok(result)

def get_ecommerce_ec_for_cielo_refund_request(refund_info: EstornoLog)-> str:

    if refund_info.tipo == 'S':

        #Veloce
        if refund_info.numeroPedidoVTEX is None or refund_info.numeroPedidoVTEX == "":
            ec_ter_info, _ = utils.get_branch_id_in_acquirer(refund_info, 102)
            return ec_ter_info.establishment
        
        #Vtex
        else:
            vtex_config = utils.get_config()['api']['vtex']
            ec = VTEXHandler(vtex_config).get_only_estableshiment_code(asdict(refund_info))
            return ec

    else:
        ec_ter_info, refund_info = utils.get_branch_id_in_acquirer(refund_info, 102)
        return ec_ter_info.establishment
    

    

if __name__ == '__main__': 

    x = track_cielo_refund_request()

    print("fim")