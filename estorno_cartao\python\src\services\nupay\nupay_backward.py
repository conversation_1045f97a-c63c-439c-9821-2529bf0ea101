from typing import List, Dict, Union, Tuple
from requests import get
from datetime import datetime
from Config.database import query_all_db, query_one_db
from models.result_handle import ResultHandle
from services.estorno_service import EstornoService
import Config.vars as vars
import services.nupay.nupay_var as nu_var
import services.nupay.nu_tools as nu_tools
from services.logger import create_logger
from models.refund_log import estorno_venda_log_orm



nu_tracker_logger = create_logger(__name__, without_handler = True)

class NupayTracker():
    
    def __init__(self, config: dict, env: str = "prd_env"): 
        """

        Args:
            `config` (dict): Parâmetro de configuração da API \n

            `env` (str, optional): Ambiente do Nupay que será usado. \n
                                test_env -> ambiente de teste \n
                                prd_env -> ambiente de produção \n
        """
        self.config = config
        self.env = env
    
    def execute(self): 

        nu_tracker_logger.info("Início da execução do retorno Nupay")

        waiting_nupay_refunds = self._find_waiting_nupay()
        estorno_service = EstornoService(self.config)

        for refund in waiting_nupay_refunds:

            refund_info_in_nupay_res = self._get_refund_info_from_nupay(refund)
            if refund_info_in_nupay_res.failure: continue

            nupay_refund_info, refund = self._verify_refund_processment(refund_info_in_nupay_res.data, refund)
            if nupay_refund_info.wait: continue

            nu_tracker_logger.info(f"Id_estorno: {refund['ID_ESTORNO']} status na nubank: {nupay_refund_info.data['status']}")

            estorno_service.update_refund_processment(estorno_venda_log_orm(refund))
        
        if len(waiting_nupay_refunds) == 0:
            nu_tracker_logger.info("Não há casos de retorno de Nupay")

        nu_tracker_logger.info("Fim da execução do retorno Nupay")

    def _find_waiting_nupay(self, days_to_go_back: int = 90) -> List[Dict]:
        """Buscar os atendimentos de pedido de estornos nupay que estão como \n
        (EM ANDAMENTO NUBANK) até `days_to_go_back` dias para trás \n

        Args:
            days_to_go_back (int, optional): Dias para voltar. \n
            Defaults to 90.

        Returns:
            List[Dict]: Lista de atendimentos de pedido de estornos nupay
        """

        waiting_nupay_query = """
        SELECT * FROM CosmosRPA.dbo.ESTORNO_VENDA_LOG (nolock)
        WHERE 
        DATA_CADASTRO >= (GETDATE() - {})
        and DATA_CADASTRO < (GETDATE() + 1)
        AND STATUS LIKE '%{}%'
        ORDER BY DATA_CADASTRO  DESC"""

        formatted_query = waiting_nupay_query.format(days_to_go_back, vars.NUPAY_ACQUIRE_NAME)

        try:
            waiting_nupay_refunds = query_all_db(formatted_query)
        except Exception as error:
            print(f"Erro ao buscar informções de estornos da nupay em espera")
            print(f"Detalhes: {error}")
            raise error


        return waiting_nupay_refunds
    
    def _get_refund_info_from_nupay(self, refund_info: Dict)-> ResultHandle:
        """Obter informações sobre o estorno na Nubank

        Args:
            refund_info (Dict): Informações sobre o estorno na pmenos

        Returns:
            ResultHandle: Informações do estorno na nubank
        """

        search_sale_url: str = self.config['nupay'][self.env]['search_refund_url']

        headers = nu_tools.make_headers(
            self.config, 
            self.env, 
            self._enterprise_code_of_the_branch(refund_info["FILIAL_ORIGEM"])
            )

        try:
            api_res = get(url= search_sale_url.format(refund_info["T_ID"], refund_info["ID_ESTORNO_ADQUIRENTE"]),
                          headers=headers)
        except Exception as error:
            msg = "Erro ao consultar venda na API"
            print(f"Detalhe do erro: {error}")
            return ResultHandle.Fail(msg)

        request_verification = nu_tools.validate_request(api_res)
        if request_verification.failure: return request_verification
        
        return request_verification

    def _enterprise_code_of_the_branch(self, branch_num: Union[int, str]) -> str:
        """Indicar qual é o código empresa para a filial

        Args:
            branch_num (Union[int, str]): Número da filial

        Returns:
            str: Código de da empresa: E.g. (`exf` ou `pm`)
        """        

        enterprise_code_query = """
        SELECT EMPR_CD_EMPRESA FROM Cosmos_v14b.dbo.FILIAL NOLOCK
        WHERE FILI_CD_FILIAL = ?
        """

        if isinstance(branch_num, str): branch_num = int(branch_num)

        try:
            enterprise_code = query_one_db(enterprise_code_query, branch_num)
            assert isinstance(enterprise_code, dict), f"Sem código de empresa para filial {branch_num}"
        except Exception as error:
            print(f"Erro ao verificar qual a empresa da filial {branch_num}")
            print(f"Detalhes: {error}")
            raise error

        if enterprise_code["EMPR_CD_EMPRESA"] == 2:
            return vars.EXTRA_FARMA_CASE
        else:
            return vars.PAGUE_MENOS_CASE
    
    def _verify_refund_processment(self, nupay_refund_info: Dict, 
                    logged_refund_info: Dict) -> Tuple[ResultHandle, Dict]:
        """Verifica o como está o processomento de estorno e decidir
        o que fazer com ele

        Args:
            nupay_refund_info (Dict): Informações do estorno que vem da Nupay
            logged_refund_info (Dict): Informações do estorno que vem do grupo pmenos

        Returns:
            Tuple[ResultHandle, Dict]: Resultado da verificação
        """

        if nupay_refund_info["status"] == nu_var.NUPAY_ERROR:
            return self._treat_refund_with_error(nupay_refund_info, logged_refund_info)
        
        if nupay_refund_info["status"] == nu_var.NUPAY_REFUNDED:
            return self._treat_sucessful_refund(nupay_refund_info, logged_refund_info)

        if nupay_refund_info["status"] == nu_var.NUPAY_WAIT_REFUND:

            register_datetime: datetime = logged_refund_info["DATA_CADASTRO"]
            retry_datetime = register_datetime.replace(hour = 23, minute = 59)

            if datetime.today() < retry_datetime: 
                return ResultHandle.Waiting("Esperando", nupay_refund_info), logged_refund_info

            return self._treat_too_much_wait(nupay_refund_info, logged_refund_info)
        
        return self._treat_new_not_mapped_status_refund(nupay_refund_info, logged_refund_info)

    def _treat_refund_with_error(self, nupay_refund_info: Dict, 
                logged_refund_info: Dict) -> Tuple[ResultHandle, Dict]:
        """Tratar o caso quando da erro no estorno

        Args:
            nupay_refund_info (Dict): Informações do estorno que vem da Nupay
            logged_refund_info (Dict): Informações do estorno que vem do grupo pmenos

        Returns:
            Tuple[ResultHandle, Dict]: Resultado do tratamento
        """

        current_refund_type_error: str = nupay_refund_info["error"]["type"]

        if current_refund_type_error not in tuple(nu_var.type_of_refund_erros_and_standard_msg.keys()):

            msg = "Novo STATUS de erro de estorno não mapeado"
            logged_refund_info["STATUS"] = vars.STATUS_PENDENCY_FOR_CSC
            logged_refund_info["MOTIVO"] = msg
            logged_refund_info["DETALHAMENTO"] = vars.PENDENCY_FOR_CSC_OBSERVATION
            return ResultHandle.Fail(msg, nupay_refund_info), logged_refund_info

        else:

            msg = nu_var.type_of_refund_erros_and_standard_msg[current_refund_type_error]
            msg = nu_tools.shrink_message(msg)
            logged_refund_info["STATUS"] = vars.STATUS_PENDENCY_FOR_CSC
            logged_refund_info["MOTIVO"] = msg
            logged_refund_info["DETALHAMENTO"] = vars.PENDENCY_FOR_CSC_OBSERVATION
            return ResultHandle.Fail(msg, nupay_refund_info), logged_refund_info
    
    def _treat_sucessful_refund(self, nupay_refund_info: Dict, 
                logged_refund_info: Dict) -> Tuple[ResultHandle, Dict]:
        """Tratar o caso quando o estorno deu certo

        Args:
            nupay_refund_info (Dict): Informações do estorno que vem da Nupay
            logged_refund_info (Dict): Informações do estorno que vem do grupo pmenos

        Returns:
            Tuple[ResultHandle, Dict]: Resultado do tratamento
        """

        logged_refund_info["STATUS"] = vars.STATUS_REFUND_PROCESS_END
        logged_refund_info["MOTIVO"] = vars.SUCCESSFUL_REFUND_REASON
        logged_refund_info["DETALHAMENTO"] = vars.REFUND_PROCESS_END_OBSERVATION_NUPAY
        logged_refund_info["DATA_RESP_ADQUIRENTE"] = datetime.now().strftime(vars.DATE_BASE_DATETIME_FORMAT)
        return ResultHandle.Ok(nupay_refund_info), logged_refund_info
    
    def _treat_too_much_wait(self, nupay_refund_info: Dict, 
                logged_refund_info: Dict) -> Tuple[ResultHandle, Dict]:
        """Tratar o caso quando espera do estorno alcança o limite

        Args:
            nupay_refund_info (Dict): Informações do estorno que vem da Nupay
            logged_refund_info (Dict): Informações do estorno que vem do grupo pmenos

        Returns:
            Tuple[ResultHandle, Dict]: Resultado do tratamento
        """
            
        msg = ("Pedido de estorno ultrapassaou o limite de espera")
        logged_refund_info["STATUS"] = vars.STATUS_PENDENCY_FOR_CSC
        logged_refund_info["MOTIVO"] = msg
        logged_refund_info["DETALHAMENTO"] = vars.PENDENCY_FOR_CSC_OBSERVATION
        return ResultHandle.Fail(msg, nupay_refund_info), logged_refund_info
    
    def _treat_new_not_mapped_status_refund(self, nupay_refund_info: Dict, 
                logged_refund_info: Dict) -> Tuple[ResultHandle, Dict]:
        """Tratar o caso quando da erro no estorno que não tem mapeamento

        Args:
            nupay_refund_info (Dict): Informações do estorno que vem da Nupay
            logged_refund_info (Dict): Informações do estorno que vem do grupo pmenos

        Returns:
            Tuple[ResultHandle, Dict]: Resultado do tratamento
        """

        msg = ("STATUS do estorno não mapeado")
        logged_refund_info["STATUS"] = vars.STATUS_PENDENCY_FOR_CSC
        logged_refund_info["MOTIVO"] = msg
        logged_refund_info["DETALHAMENTO"] = vars.PENDENCY_FOR_CSC_OBSERVATION
        logged_refund_info["DATA_RESP_ADQUIRENTE"] = datetime.now().strftime(vars.DATE_BASE_DATETIME_FORMAT)
        return ResultHandle.Fail(msg, nupay_refund_info), logged_refund_info

   



