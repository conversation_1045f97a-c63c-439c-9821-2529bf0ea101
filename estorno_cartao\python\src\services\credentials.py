from botcity.maestro import*
from typing import Union,List,Tuple

class Credentials:
    def __init__(self) -> None:
        self.maestro = BotMaestroSDK()
        self.server = "https://paguemenos.botcity.dev/"
        self.login = "pmenos"
        self.key = "PME_PDDJFSYKUJRJ405QLZ2L"
        
    
    def get_credentials(self,label,key)->Tuple[str,int]:
        try:
            self.maestro.login(server=self.server, login=self.login, key=self.key)
            cred = self.maestro.get_credential(label=label,key=key)  
            return cred, 0
              
        except Exception as e:
            return e, -1    