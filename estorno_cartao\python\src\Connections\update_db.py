import Connections.scripts as scripts
from Connections.dao import DAO
from typing import Dict
from datetime import  datetime
from models.refund_log import EstornoLog

class UpdateDb:

    def __init__(self):
        self.dao = DAO()


    def update_method(self, dados):
        """<PERSON>éto<PERSON> que recebe um objeto com os dados, obtem uma conexão, formata uma query específica, realiza atualização e fecha a conexão"""
        nome_do_servidor = "cosmos"
        conn = self.dao.create_connection(db=nome_do_servidor)
        cursor = conn.cursor()
        try:
           
            sql = scripts.UPDATE_TOKEN.format(*dados) 
            print(sql)
            cursor.execute(sql)
            
            cursor.commit()
            
            return None, 0

        except Exception as e:
            print(f"Não foi possível atualizar. Detalhe: {e}")
            return None, -5
        finally:
            self.dao.close(conn)

    
    def update_processment(self, refund_info: EstornoLog):
        """Método que recebe um objeto com os dados, obtem uma conexão, formata uma query específica, realiza atualização e fecha a conexão"""
        nome_do_servidor = "cosmos"
        conn = self.dao.create_connection(db=nome_do_servidor)
        cursor = conn.cursor()
        try:

            if refund_info.data_resp_adquirente is None:
                sql = scripts.GENERAL_REFUND_PROCESSMENT_STEP_UPDATE.format(motive=refund_info.motivo, 
                                                           status=refund_info.status, 
                                                           detail=refund_info.detalhamento, 
                                                           refund_id=refund_info.idEstorno)
            else:
                sql = scripts.REFUND_PROCESSMENT_STEP_UPDATE_WITH_ACQUIRENTE_RESPONSE_DATE.format(motive=refund_info.motivo, 
                                                                                           status=refund_info.status, 
                                                                                           detail=refund_info.detalhamento,
                                                                                           acquirente_response_date=refund_info.data_resp_adquirente,
                                                                                           refund_id=refund_info.idEstorno)

            cursor.execute(sql)
            
            cursor.commit()
            
            return None, 0

        except Exception as e:
            print(f"Não foi possível atualizar. Detalhe: {e}")
            raise e
        finally:
            self.dao.close(conn)

    def record_zendesk_ticket_id(self,refund_info_id : str, at_which_stage: str, zendesk_ticket_id: int ):
        
        nome_do_servidor = "cosmos" 
        conn = self.dao.create_connection(db=nome_do_servidor)
        cursor = conn.cursor()
        
        zendesk_ticket_id = zendesk_ticket_id if isinstance(zendesk_ticket_id, str) else int(zendesk_ticket_id)

        try:

            sql = scripts.REGISTER_TICKET_ZENDESK.format(zendesk_ticket_id, at_which_stage, refund_info_id) 

            cursor.execute(sql)
            cursor.commit()
            
            return None, 0

        except Exception as e:
            print(f"Não foi possível atualizar. Detalhe: {e}")
            raise e 
        finally:
            self.dao.close(conn)

    def record_ticket_finalization(self,refund_request_processment: EstornoLog, 
                                   ending_date: datetime,
                                   refund_request_manual_processment_status: str):
        
        nome_do_servidor = "cosmos" 
        conn = self.dao.create_connection(db=nome_do_servidor)
        cursor = conn.cursor()
        

        try:

            cursor.execute(scripts.REGISTER_TICKET_FINALIZATION,
                            refund_request_manual_processment_status,
                            refund_request_processment.status,
                            refund_request_processment.detalhamento,
                            ending_date, 
                            refund_request_processment.idEstorno)
            cursor.commit()
            
            return None, 0

        except Exception as e:
            print(f"Não foi possível atualizar. Detalhe: {e}")
            return None, -5
        finally:
            self.dao.close(conn)
    
    
    def change_status_and_observation(self, refund_request_processment: EstornoLog):
        
        nome_do_servidor = "cosmos" 
        conn = self.dao.create_connection(db=nome_do_servidor)
        cursor = conn.cursor()
        

        try:

            cursor.execute(scripts.CHANGE_STATUS_AND_DETALHAMENTO,
                            refund_request_processment.status,
                            refund_request_processment.detalhamento, 
                            refund_request_processment.idEstorno)
            cursor.commit()
            
            return None, 0

        except Exception as e:
            print(f"Não foi possível atualizar. Detalhe: {e}")
            return None, -5
        finally:
            self.dao.close(conn)  
    

    def change_status_and_observation_manul_ref_process_status(self, refund_request_processment: EstornoLog, 
                                                               refund_request_manual_processment_status: str):
        
        nome_do_servidor = "cosmos" 
        conn = self.dao.create_connection(db=nome_do_servidor)
        cursor = conn.cursor()
        

        try:

            cursor.execute(scripts.CHANGE_STATUS_AND_DETALHAMENTO_STATUS_BPM,
                            refund_request_processment.status,
                            refund_request_processment.detalhamento, 
                            refund_request_manual_processment_status,
                            refund_request_processment.idEstorno)
            cursor.commit()
            
            return None, 0

        except Exception as e:
            print(f"Não foi possível atualizar. Detalhe: {e}")
            return None, -5
        finally:
            self.dao.close(conn)  
    

    
            