import requests
from requests_oauthlib import OAuth1
import json

class FluigServices:

    def __init__(self,config) -> None:
        self.consumer_key = config['api']['fluig']['consumer_key']
        self.consumer_secret = config['api']['fluig']['consumer_secret']
        self.access_token = config['api']['fluig']['access_token']
        self.access_token_secret = config['api']['fluig']['access_token_secret']
        self.url = config['api']['fluig']['url']
        

    def run_activity(self,instace,type):

        # types 
        #  # estorno_finalizado
        #  # reprocessamento

        auth = OAuth1(self.consumer_key, self.consumer_secret, self.access_token, self.access_token_secret)

        headers = {'Content-Type': 'application/json'}

        data = json.dumps({ 
        "name": "ds_opf_movimentar_tarefa", 
        "fields": None, 
            "constraints": [
                {
                    "_field":"numero_processo",
                    "_initialValue": instace,
                    "_finalValue": instace,
                    "_type":1,
                    "_likeSearch":False
                },
                {
                    "_field":"situacao",
                    "_initialValue":type,
                    "_finalValue":type,
                    "_type":1,
                    "_likeSearch":False
                }
            ],
            "order": None
        })

        response = requests.post(self.url, headers=headers, data=data, auth=auth)

        print(response.text)

