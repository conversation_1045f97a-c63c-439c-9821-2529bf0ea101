import requests 
import json
import utils

class BuscarVenda():
    
    def __init__(self, config:dict):            
        self.__api_base_url = config['api']['movimento-bpm']['api_base_url']
        self.__resource_getnet = config['resource']['getnet']
        
        
        super().__init__()

    def venda_getnet(self):
        response = requests.get(self.__api_base_url.format(self.__resource_getnet))
        response_dict = response.text
        retorno_venda = response_dict
        return json.loads(retorno_venda)

    

    