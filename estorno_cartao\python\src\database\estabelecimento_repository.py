import sqlite3


class EstabelecimentoRepository():
    
    def __init__(self, connection_string, db_name='estab.sqlite'):
        """[summary]

        Args:
            connection_string ([type]): [description]
            db_name (str, optional): [description]. Defaults to 'estab.sqlite'.
        """        
        self.connection_string = connection_string
        self.db_name = db_name

    def __get_connection(self):
        conn_string = self.connection_string
        try:
            conn = sqlite3.connect(conn_string+self.db_name)
        except Exception as e:
            raise(e)
        else:
            return conn

    def select_codigo_estab(self, adquirente:str, plataforma:str, filial:int) -> str:
        try:
            with self.__get_connection() as conn:
                cursor = conn.cursor()
                _sql = self.__get_sql_script('select_estabelecimento')                
                result_set = list(cursor.execute(
                    _sql.format(adquirente, plataforma, filial)))
                cursor.close()
        except Exception as e:
            raise(e)
        else:
            return result_set[0][0] if result_set else 'not found'

    def select_filial_by_estab(self, estabelecimento:str) -> str:
        try:
            with self.__get_connection() as conn:
                cursor = conn.cursor()
                _sql = self.__get_sql_script('select_filial_by_estab')                
                result_set = list(cursor.execute(
                    _sql.format(estabelecimento)))
                cursor.close()
        except Exception as e:
            raise(e)
        else:
            return result_set[0][0] if result_set else 'not found'
    
    def __get_sql_script(self, script_name:str) -> str:
        sql_script = ''
        if script_name.lower() == 'select_estabelecimento':
            sql_script = '''
                            SELECT CODIGO_ESTABELECIMENTO 
                                FROM ESTABELECIMENTO 
                            WHERE LOWER(ADQUIRENTE) = LOWER('{}')
                                AND PLATAFORMA = '{}'
                                AND FILIAL = {}
                            LIMIT 1
                        '''
        elif script_name.lower() == 'select_filial_by_estab':
            sql_script = '''
                            SELECT FILIAL 
                                FROM ESTABELECIMENTO 
                                WHERE CODIGO_ESTABELECIMENTO = '{}'
                            LIMIT 1
                        '''
        else:
            raise ValueError('Script desconhecido')
        return sql_script
