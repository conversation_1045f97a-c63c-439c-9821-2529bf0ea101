import pytest
from src.services.refund_api.refunds_info import Refund<PERSON>pi
from src.Config.vars import PAGUE_MENOS_CASE, EXTRA_FARMA_CASE


@pytest.mark.parametrize("pos_kind, enterprise", [
    ('S', PAGUE_MENOS_CASE),
    ('S', EXTRA_FARMA_CASE),
    ('N', PAGUE_MENOS_CASE),
    ('N', EXTRA_FARMA_CASE),
])
class TestApiRefund():

    def test_get_not_processed_refunds_by_days_back_and_pos_kind(self, pos_kind, enterprise):

        result = RefundApi().get_not_processed_refunds_by_days_back_and_pos_kind(90, pos_kind, enterprise)

        assert result.success, f"Erro ao obter estornos não processados. POS_KIND: {pos_kind} - ENTERPRISE: {enterprise}"

    def test_get_not_processed_refunds_by_days_back_and_pos_kind_error(self, pos_kind, enterprise):

        result = RefundApi().get_not_processed_refunds_by_days_back_and_pos_kind('90', pos_kind, enterprise)

        assert result.failure, f"Foi possível obter estornos não processados. POS_KIND: {pos_kind} - ENTERPRISE: {enterprise}"


if __name__ == '__main__':
    pytest.main([__file__])

