from requests.models import Response
from typing import Dict
from pathlib import Path
import sys

def local_execution_with_others_projects_modules(current_file: str, directory_to_go_back: int = 1) -> Path:
    """
    If in a local execution the current module uses resources from another \n
    module that is in the same project i.e.(this module is in the same \n
    directory as the other module) and they aren't in the same level. \n
    So you need to add the common dictory between the modules to the Paths of execution \n
    
    Args:
        `current_file` (str): file path who calls the other modules

    Returns:
        `Path`: common directory Path
    """
    
    src_folder = Path(current_file.split(":")[0].upper() + ":" + current_file.split(":")[1])
    for _ in range(directory_to_go_back): src_folder = src_folder.parent
    if src_folder.__str__() not in sys.path: sys.path.append(str(src_folder))
    if Path.cwd().__str__() not in sys.path: sys.path.append(Path.cwd().__str__())

    return src_folder

if __name__ == "__main__":
    src_folder = local_execution_with_others_projects_modules(__file__, 3)

import Config.vars as vars
import services.nupay.nupay_var as nu_var
from utils import ResultHandle



def validate_request(api_res: Response) -> ResultHandle:
    """Validar reposta da api

    Args:
        api_res (Response): Resposta da API

    Returns:
        ResultHandle: Objesto de resultado
    """
    
    if api_res.status_code in tuple(nu_var.request_error_and_standard_msg.keys()):
        msg = nu_var.request_error_and_standard_msg[api_res.status_code]
        return make_request_result(api_res, True, msg)
    
    if api_res.status_code != 200:
        return make_request_result(api_res, True, "Resposta de API não foi mapeada")

    return make_request_result(api_res, False)

def make_request_result(api_res: Response, is_error: bool, msg: str = None) -> ResultHandle:
    """Criar objeto de resultado para requisição

    Args:
        api_res (Response): resultado da API
        is_error (bool): Indica se houve erro na requisição
        msg (str, optional): Mensagem do resultado

    Returns:
        ResultHandle: Objeto de resultado
    """

    body_response: Dict = api_res.json()

    if "message" in tuple(body_response.keys()) and msg is None:

        msg = body_response["message"]

        for error_translation in nu_var.translated_erros:
            if error_translation.key_string in msg:
                msg = error_translation.translation
    
    msg = shrink_message(msg)
        
    if is_error: 
        print(msg)
        return ResultHandle.Fail(msg, api_res.json())

    return ResultHandle.Ok(api_res.json())

def make_headers(config: Dict, env: str, entreprise: str):
    """Criar os headers padrões das requisições da API da Nupay

    Args:
        config (Dict): Objeto com dados de configuração 
        env (str): Para qual ambiente será configurado
        entreprise (str): Indicar o token da empresa

    Returns:
        Dict: Dicionário com os headers
    """

    headers = {
    'Content-Type': 'application/json',
    'X-Merchant-Token': config['nupay'][env][entreprise]["X-Merchant-Token"],
    'X-Merchant-Key': config['nupay'][env][entreprise]["X-Merchant-Key"]
    }

    return headers

def shrink_message(msg: str, max_lenght_of_chars: int = 254) -> str:
    """Cortar a messagem de modo a não dar erro de overflow no banco

    Args:
        msg (str): String da menssagem 
        max_lenght_of_chars (int, optional): Tamanho máximo da string

    Returns:
        str: Mensagem cortada
    """

    if msg is not None and len(msg) > max_lenght_of_chars:
        msg = msg[:254]
        return msg
    else:
        return msg



