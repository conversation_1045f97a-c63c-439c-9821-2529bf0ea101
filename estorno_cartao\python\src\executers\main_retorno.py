from services.getnet_service import ServiceGetnet
from services.estorno_service import EstornoService
from services.cielo_service import ServiceCielo
from Connections.search_db import SearchDb
from models.refund_log import *
from services.multi_report_handler import *
from services.getnet.getnet_utils import find_getnet_refund_protocol_with_sale_info
from services.cielo.cielo_utils import  get_ecommerce_ec_for_cielo_refund_request
from services.logger import create_logger
from typing import List, Dict, Tuple, Union
from Config.database import query_all_db
from dataclasses import asdict
from models.refund_log import EstornoLog
import Config.vars as global_vars
import utils
from datetime import datetime

if __name__ == '__main__': 
    acquire_return_logger = create_logger('')
    acquire_return_logger.setLevel(20)
else: 
    acquire_return_logger = create_logger(__name__, without_handler = True)


class RetornoRequisicao():

    def __init__(self, app_config):
        
        self.config = app_config
        self.estorno_service = EstornoService(app_config)
        self.getnet_service = ServiceGetnet(app_config)
        self.service_cielo = ServiceCielo(app_config)
        self.search = SearchDb()

        super().__init__()
    

    def processar_retorno_getnet(self):

        msg = "Iniciando acampanhamento dos pedidos de estorno da GETNET"
        acquire_return_logger.info(msg)
        log_report(msg)

        getnet_refund_requests_result = self.track_waiting_refund_requests(global_vars.GETNET_ACQUIRE_SCOPE_CODE)

        if getnet_refund_requests_result.failure:
            acquire_return_logger.info(getnet_refund_requests_result.error_description)
            log_report(getnet_refund_requests_result.error_description)
            return -1
        
        if getnet_refund_requests_result.wait:
            acquire_return_logger.info(getnet_refund_requests_result.error_description)
            log_report(getnet_refund_requests_result.error_description)
            return 0
        
        getnet_refund_requests: List[EstornoLog] = getnet_refund_requests_result.data
        
        for refund_info in getnet_refund_requests:

            try:
                acquire_return_logger.info(f"Verificando Pedido getnet id_estorno: {refund_info.idEstorno}")

                if refund_info.protocoloGetnet is None:
                    venda_restult = find_getnet_refund_protocol_with_sale_info(refund_info)
                    if venda_restult.failure: 
                        continue

                    venda: Dict = venda_restult.data
                else:
                    venda = asdict(refund_info)
                
                motivo, code = self.getnet_service.request_getnet_service(venda['protocoloGetnet'], refund_info.nomeEmpresa)
                if code == '100': continue
                
                refund_info = self.format_refund_result(motivo, code, refund_info, global_vars.GETNET_ACQUIRE_SCOPE_CODE)

                self.estorno_service.update_refund_processment(refund_info)

            except Exception as e:
                if refund_info.idEstorno is not None:
                    msg = f"Erro ao verificar o pedido de estorno da getnet. id_estorno:{refund_info.idEstorno}"
                else:
                    msg = "Erro ao verificar o pedido de estorno da getnet"

                log_report(msg)
                acquire_return_logger.error(msg)
                acquire_return_logger.error(f"Detalhes do erro: {e}")
                acquire_return_logger.info(f"Indo para a próximo pedido de estorno")
                continue

        msg = "Fim acampanhamento dos pedidos de estorno da getnet"
        acquire_return_logger.info(msg)
        log_report(msg)

            
    def processar_retorno_cielo(self) -> None:
        
        msg = "Iniciando acampanhamento dos pedidos de estorno da Cielo"
        acquire_return_logger.info(msg)
        log_report(msg)

        cielo_refund_requests_result= self.track_waiting_refund_requests(global_vars.CIELO_ACQUIRE_SCOPE_CODE)

        if cielo_refund_requests_result.failure:
            acquire_return_logger.info(cielo_refund_requests_result.error_description)
            log_report(cielo_refund_requests_result.error_description)
            return -1
        
        if cielo_refund_requests_result.wait:
            acquire_return_logger.info(cielo_refund_requests_result.error_description)
            log_report(cielo_refund_requests_result.error_description)
            return 0
        
        cielo_refund_requests: List[EstornoLog] = cielo_refund_requests_result.data
        
        for refund_info in cielo_refund_requests:
            try:
                acquire_return_logger.info(f"Verificando Pedido cielo id_estorno: {refund_info.idEstorno}")

                if refund_info.protocoloCielo is None:
                    msg = "Protocolo cielo nao encontrado. Falar com time de RPA"
                    refund_info.status = global_vars.STATUS_PENDENCY_FOR_CSC
                    refund_info.motivo = msg
                    acquire_return_logger.info(msg)
                    log_report(msg)
                    self.estorno_service.update_refund_processment(refund_info)
                    continue
                
                ec = get_ecommerce_ec_for_cielo_refund_request(refund_info)
                motivo, code = self.service_cielo.consultacielo(ec, refund_info.protocoloCielo, refund_info.nomeEmpresa)
                
                if code == -1:
                    log_report(motivo)
                    continue
                
                if code in [0]:
                    acquire_return_logger.info(f"Pedido cielo em espera id_estorno")
                    continue
                                                
                refund_info = self.format_refund_result(motivo, code, refund_info, global_vars.CIELO_ACQUIRE_SCOPE_CODE)

                self.estorno_service.update_refund_processment(refund_info)

            except Exception as e:
                if refund_info.idEstorno is not None:
                    msg = f"Erro ao verificar o pedido de estorno da cielo. id_estorno:{refund_info.idEstorno}"
                else:
                    msg = "Erro ao verificar o pedido de estorno da cielo"

                log_report(msg)
                acquire_return_logger.error(msg)
                acquire_return_logger.error(f"Detalhes do erro: {e}")
                acquire_return_logger.info(f"Indo para a próximo pedido de estorno")
                continue

        msg = "Fim acampanhamento dos pedidos de estorno da Cielo"
        acquire_return_logger.info(msg)
        log_report(msg)
    
    def format_refund_result(self, result_detail: str, 
                             result_code: Union[str, int],
                             refund_info: EstornoLog,
                             acquire_code: int):
        
        result_code = str(result_code) if isinstance(result_code, int) else result_code

        confirmation_response, negative_response, acquire_name = self.get_results_template(acquire_code)
        if result_code in confirmation_response:

            refund_info.data_resp_adquirente = datetime.now().strftime(global_vars.DATE_BASE_DATETIME_FORMAT)
            refund_info.status = global_vars.STATUS_WAIT_PROOF_OF_REFUND
            refund_info.motivo = result_detail
            refund_info.detalhamento = global_vars.PROOF_OF_REFUND_OBSERVATION

            msg = f"Pedido de estorno {acquire_name} id_estorno: {refund_info.idEstorno} finalizado"
            acquire_return_logger.info(msg)
            log_report(msg)

        elif result_code in negative_response:

            refund_info.status  = global_vars.STATUS_PENDENCY_FOR_CSC
            refund_info.motivo = result_detail
            refund_info.detalhamento = global_vars.PENDENCY_FOR_CSC_OBSERVATION
            msg = f"Pedido de estorno {acquire_name} com erro id_estorno: {refund_info.idEstorno}"

            acquire_return_logger.info(msg)
            log_report(msg)

        else:

            acquire_return_logger.warning(f"Status de reposta do acompanhamento {acquire_name} não identificado: {result_code}")
            refund_info.status = global_vars.STATUS_PENDENCY_FOR_CSC
            refund_info.motivo = result_detail
            refund_info.detalhamento = global_vars.PENDENCY_FOR_CSC_OBSERVATION

            msg = f"Pedido de estorno {acquire_name} com erro id_estorno: {refund_info.idEstorno}"
            
            acquire_return_logger.info(msg)
            log_report(msg)
        
        return refund_info


    def get_results_template(self, acquire_code: int)-> Tuple[List[str], List[str], str]:

        if acquire_code == global_vars.CIELO_ACQUIRE_SCOPE_CODE:
            confirmation_response = utils.CIELO_VALIDOS
            negative_response = utils.CIELO_INVALIDOS

            return confirmation_response, negative_response, global_vars.CIELO_ACQUIRE_NAME

        elif acquire_code == global_vars.GETNET_ACQUIRE_SCOPE_CODE:
            confirmation_response = utils.GETNET_VALIDOS
            negative_response = utils.GETNET_INVALIDOS

            return confirmation_response, negative_response, global_vars.GETNET_ACQUIRE_NAME
        
        else:
            acquire_return_logger.error(f"Código de adquirente não mapeada: {acquire_code}")
            raise NotImplementedError(f"Não há tratativa para código de adquirente {acquire_code}")

    def track_waiting_refund_requests(self, acquire_code: int)-> global_vars.ResultHandle:
        """Quais são os pedidos de estorno em andamento de acordo com a adquirente pedida

        Returns:
            global_vars.ResultHandle: quando sucesso List[EstornoLog]
        """    
        if acquire_code == global_vars.CIELO_ACQUIRE_SCOPE_CODE:
            waiting_status = global_vars.CIELO_ACQUIRE_NAME
            current_acquire_name =global_vars.CIELO_ACQUIRE_NAME

        elif acquire_code == global_vars.GETNET_ACQUIRE_SCOPE_CODE:
            waiting_status = global_vars.GETNET_ACQUIRE_NAME
            current_acquire_name = global_vars.GETNET_ACQUIRE_NAME

        else:
            acquire_return_logger.error(f"Código de adquirente não mapeada: {acquire_code}")
            raise NotImplementedError(f"Não há tratativa para código de adquirente {acquire_code}")

        TRACK_WAITING_REFUND = f"""
                        SELECT *
                        FROM CosmosRPA.dbo.ESTORNO_VENDA_LOG ev_log (nolock)
                        WHERE 
                        ev_log.STATUS like '%{waiting_status}%'
                        ORDER BY DATA_CADASTRO ASC
                        """

        try:
            refund_requests = query_all_db(TRACK_WAITING_REFUND)
            result = list(map(estorno_venda_log_orm, refund_requests))

        except Exception as e:
            msg = f"Erro ao buscar estorno em andamento da {current_acquire_name}"
            acquire_return_logger.error(msg)
            acquire_return_logger.error(f"Detalhes do erro: {e}")
            return global_vars.ResultHandle.Fail(f"{msg} | {e}")

        if len(refund_requests) == 0:
            msg = f"Não foi encontrado nenhum estorno em andamento na {current_acquire_name}"
            acquire_return_logger.info(msg)
            return global_vars.ResultHandle.Waiting(error=msg)

        
        return global_vars.ResultHandle.Ok(result)
            

