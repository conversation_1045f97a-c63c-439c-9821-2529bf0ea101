{"audits": [{"id": 35428185214349, "ticket_id": 1462310, "created_at": "2025-03-30T00:54:55Z", "author_id": 30108526245517, "metadata": {"system": {"client": "python-requests/2.27.1", "ip_address": "**************", "location": "<PERSON><PERSON>, MG, Brazil", "latitude": -21.749, "longitude": -43.4382}, "custom": {}}, "events": [{"id": 35428185214477, "type": "Comment", "author_id": 27553558364301, "body": "Houve um problema no atendimento automatizado de estorno. Ele foi direcionado para o setor de CSC\n\nDetalhes do estorno:\n\nVenda do tipo Pix Mercad com finalizador None\n\n**Motivo:** Venda Pix Não localizada na Veloce POS\n\n**Nome do cliente:** MARIA PAULA LOPES MOREIRA COSTA\n\n**Filial da venda:** 7362; **CNPJ filial da venda:** 004.899.316/0362-28; **Filial do pedido de estorno:** 7362;\n\n**NSU TEF:** 0; **NSU HOST:** 0; **TID:** None;\n\n**Nº cupom:** 25563; **COO:** 85606; **Número pedido E-commerce:** ;\n\n**Telefone:** 85991088149; **e-mail:** <EMAIL>; **cartão sempre:** 31426482\n\n**Nº troca-devolução:** 2705141; **ID estorno:** 384429; **Nº pré-venda:** 306249214;\n\n**Código de autorização:** None; **Dígitos do cartão:** None;", "html_body": "<div class=\"zd-comment\" dir=\"auto\"><p dir=\"auto\">Houve um problema no atendimento automatizado de estorno. Ele foi direcionado para o setor de CSC</p>\n    <p dir=\"auto\">Detalhes do estorno:</p>\n    <p dir=\"auto\">Venda do tipo Pix Mercad com finalizador None</p>\n\n    <p dir=\"auto\"><strong>Motivo: </strong>Venda Pix Não localizada na Veloce POS</p>\n\n    <p dir=\"auto\"><strong>Nome do cliente: </strong>MARIA PAULA LOPES MOREIRA COSTA</p>\n\n    <p dir=\"auto\">\n        <strong>Filial da venda: </strong> 7362;\n        <strong>CNPJ filial da venda: </strong> 004.899.316/0362-28;\n        <strong>Filial do pedido de estorno: </strong> 7362; \n    </p>\n\n    <p dir=\"auto\">\n        <strong>NSU TEF: </strong> 0; \n        <strong>NSU HOST: </strong> 0;\n        <strong>TID: </strong> None;\n    </p>\n\n    <p dir=\"auto\">\n        <strong>Nº cupom: </strong> 25563; \n        <strong>COO: </strong> 85606; \n        <strong>Número pedido E-commerce: </strong> ; \n    </p>\n\n    <p dir=\"auto\">\n        <strong>Telefone: </strong> 85991088149; \n        <strong>e-mail: </strong> <EMAIL>; \n        <strong>cartão sempre: </strong> 31426482\n    </p>\n\n    <p dir=\"auto\">\n        <strong>Nº troca-devolução: </strong> 2705141; \n        <strong>ID estorno: </strong> 384429;\n        <strong>Nº pré-venda: </strong> 306249214;\n    </p>\n\n    <p dir=\"auto\">\n        <strong>Código de autorização: </strong> None; \n        <strong>Dígitos do cartão: </strong> None;\n    </p></div>", "plain_body": "Houve um problema no atendimento automatizado de estorno. Ele foi direcionado para o setor de CSC \n     Detalhes do estorno: \n     Venda do tipo Pix Mercad com finalizador None \n\n     Motivo: Venda Pix Não localizada na Veloce POS \n\n     Nome do cliente: MARIA PAULA LOPES MOREIRA COSTA \n\n     \n        Filial da venda:  7362;\n        CNPJ filial da venda:  004.899.316/0362-28;\n        Filial do pedido de estorno:  7362; \n     \n\n     \n        NSU TEF:  0; \n        NSU HOST:  0;\n        TID:  None;\n     \n\n     \n        Nº cupom:  25563; \n        COO:  85606; \n        Número pedido E-commerce:  ; \n     \n\n     \n        Telefone:  85991088149; \n        e-mail:  <EMAIL>; \n        cartão sempre:  31426482\n     \n\n     \n        Nº troca-devolução:  2705141; \n        ID estorno:  384429;\n        Nº pré-venda:  306249214;\n     \n\n     \n        Código de autorização:  None; \n        Dígitos do cartão:  None;", "public": true, "attachments": [], "audit_id": 35428185214349}, {"id": 35428185214605, "type": "Create", "value": "27354928086413", "field_name": "brand_id"}, {"id": 35428185214733, "type": "Create", "value": "26546858689677", "field_name": "group_id"}, {"id": 35428185214861, "type": "Create", "value": "27553558364301", "field_name": "requester_id"}, {"id": 35428185214989, "type": "Create", "value": "RPA - Estorno - valor 16.92 - MARIA PAULA LOPES MOREIRA COSTA", "field_name": "subject"}, {"id": 35428185215117, "type": "Create", "value": "26546968004109", "field_name": "ticket_form_id"}, {"id": 35428185215245, "type": "Create", "value": ["7362_eus03_eusdeque_ce_ef", "anexo_comprovante_pos_via_loja", "atualizador_agente", "cancelamento_cancelamento_venda_em_pix", "lojas_extrafarma", "ptp_otc_cancelamento", "tipo_venda_pix_poslio", "venda_finalizada_sim"], "field_name": "tags"}, {"id": 35428185215373, "type": "Create", "value": "tipo_venda_pix_poslio", "field_name": "26503899038349"}, {"id": 35428185215501, "type": "Create", "value": "2025-03-29", "field_name": "26503899190157"}, {"id": 35428185215629, "type": "Create", "value": "MARIA PAULA LOPES MOREIRA COSTA", "field_name": "26503906400909"}, {"id": 35428185215757, "type": "Create", "value": "lojas_extrafarma", "field_name": "26504306260749"}, {"id": 35428185215885, "type": "Create", "value": "cancelamento_cancelamento_venda_em_pix", "field_name": "26503910871949"}, {"id": 35428185216013, "type": "Create", "value": "0", "field_name": "27486246039309"}, {"id": 35428185216141, "type": "Create", "value": "16,92", "field_name": "27564388165645"}, {"id": 35428185216269, "type": "Create", "value": "7362_eus03_eusdeque_ce_ef", "field_name": "27259375759757"}, {"id": 35428185216397, "type": "Create", "value": "16,92", "field_name": "27564327477005"}, {"id": 35428185216525, "type": "Create", "value": "Venda Pix Não localizada na Veloce POS", "field_name": "26503905836941"}, {"id": 35428185216653, "type": "Create", "value": "2705141", "field_name": "33697924658573"}, {"id": 35428185216781, "type": "Create", "value": "venda_finalizada_sim", "field_name": "26503889163021"}, {"id": 35428185216909, "type": "Create", "value": "0", "field_name": "26503914135309"}, {"id": 35428185217037, "type": "Create", "value": "0", "field_name": "26503888721421"}, {"id": 35428185217165, "type": "Create", "value": "0", "field_name": "28062908323597"}, {"id": 35428185217293, "type": "Create", "value": "0", "field_name": "26503938841741"}, {"id": 35428185217421, "type": "Create", "value": "0", "field_name": "26503910689165"}, {"id": 35428185217549, "type": "Create", "value": "0", "field_name": "26503944419853"}, {"id": 35428185217677, "type": "Create", "value": "0", "field_name": "26503955275021"}, {"id": 35428185217805, "type": "Create", "value": "0", "field_name": "26503916389773"}, {"id": 35428185217933, "type": "Create", "value": "0", "field_name": "26503936000013"}, {"id": 35428185218061, "type": "Create", "value": "0", "field_name": "26503916779021"}, {"id": 35428185218189, "type": "Create", "value": "0", "field_name": "26503967729549"}, {"id": 35428185218317, "type": "Create", "value": "0", "field_name": "28301149944973"}, {"id": 35428185218445, "type": "Create", "value": "0", "field_name": "28302727916685"}, {"id": 35428185218573, "type": "Create", "value": "0", "field_name": "26504533547917"}, {"id": 35428185218701, "type": "Create", "value": "0", "field_name": "28302888560525"}, {"id": 35428185218829, "type": "Create", "value": "0", "field_name": "28062899418253"}, {"id": 35428185218957, "type": "Create", "value": "ptp_otc_cancelamento", "field_name": "26921919394957"}, {"id": 35428185219085, "type": "Create", "value": "1", "field_name": "28062955735437"}, {"id": 35428185219213, "type": "Create", "value": "0", "field_name": "27742843895309"}, {"id": 35428185219341, "type": "Create", "value": "0", "field_name": "26972777392525"}, {"id": 35428185219469, "type": "Create", "value": "0", "field_name": "27305252599949"}, {"id": 35428185219597, "type": "Create", "value": "atualizador_agente", "field_name": "27442246071693"}, {"id": 35428185219725, "type": "Create", "value": null, "field_name": "priority"}, {"id": 35428185219853, "type": "Create", "value": null, "field_name": "type"}, {"id": 35428185219981, "type": "Create", "value": "new", "field_name": "status"}, {"id": 35428185220109, "type": "Create", "value": "18758522787725", "field_name": "custom_status_id"}, {"id": 35428185220237, "type": "Change", "via": {"channel": "rule", "source": {"from": {"deleted": false, "title": "Set Tickets - Prioridade Normal", "id": 26943116808717, "revision_id": 8}, "rel": "trigger"}}, "value": "normal", "field_name": "priority", "previous_value": null}, {"id": 35428185220365, "type": "ScheduleAssignment", "via": {"channel": "rule", "source": {"from": {"deleted": false, "title": "Business Hour - CSC", "id": 28470985032077, "revision_id": 2}, "rel": "trigger"}}, "previous_schedule_id": null, "new_schedule_id": "26545046365581"}, {"id": 35428185220493, "type": "Notification", "via": {"channel": "rule", "source": {"from": {"deleted": false, "title": "Notificação abertura de ticket - Geral", "id": 28060921154317, "revision_id": 12}, "rel": "trigger"}}, "subject": "[Solicitação recebida] - Assunto - {{ticket.title}}  - {{ticket.id}}", "body": "Sua solicitação foi aberta com o ticket {{ticket.id}}, \npara acompanhar o andamento, entre na aba \"Meus chamados\",<a href=https://colaborador.zendesk.com/hc/pt-br/requests> clique aqui!</a>.\n\nAtenciosamente\n\n", "recipients": [27553558364301]}, {"id": 35428185220621, "type": "Change", "via": {"channel": "rule", "source": {"from": {"deleted": false, "title": "Formulario Campos Padrões Obrigatorios", "id": 34165046325517, "revision_id": 3}, "rel": "trigger"}}, "value": ["7362_eus03_eusdeque_ce_ef", "anexo_comprovante_pos_via_loja", "atualizador_agente", "cancelamento_cancelamento_venda_em_pix", "form_default_fields_yes", "lojas_extrafarma", "ptp_otc_cancelamento", "tipo_venda_pix_poslio", "venda_finalizada_sim"], "field_name": "tags", "previous_value": ["7362_eus03_eusdeque_ce_ef", "anexo_comprovante_pos_via_loja", "atualizador_agente", "cancelamento_cancelamento_venda_em_pix", "lojas_extrafarma", "ptp_otc_cancelamento", "tipo_venda_pix_poslio", "venda_finalizada_sim"]}, {"id": 35428185220749, "type": "Change", "via": {"channel": "rule", "source": {"from": {"deleted": false, "title": "Formulario Campos Padrões Obrigatorios", "id": 34165046325517, "revision_id": 3}, "rel": "trigger"}}, "value": "form_default_fields_yes", "field_name": "34113031835149", "previous_value": null}, {"id": 35428185221645, "type": "Create", "value": "CSC - 11 Horas", "field_name": "sla_policy"}], "via": {"channel": "api", "source": {"from": {}, "to": {"address": "<EMAIL>", "name": "ef7362"}, "rel": null}}}, {"id": 35428185237645, "ticket_id": 1462310, "created_at": "2025-03-30T00:54:55Z", "author_id": -1, "metadata": {"system": {}, "custom": {}, "parent_audit_id": "35428185214349"}, "events": [{"id": 35428232468621, "type": "Change", "previous_value": null, "value": {"minutes": 660, "seconds": 39600, "in_business_hours": true}, "field_name": "agent_work_time", "via": {"source": {"rel": "sla_target_change"}, "current_sla_policy": "CSC - 11 Horas"}}], "via": {"channel": "sla", "source": {"from": {}, "to": {}, "rel": null}}}, {"id": 35437940894605, "ticket_id": 1462310, "created_at": "2025-03-30T21:05:07Z", "author_id": 29027579134605, "metadata": {"system": {"client": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "ip_address": "***************", "location": "Rio de Janeiro, RJ, Brazil", "latitude": -22.9072, "longitude": -43.1883}, "custom": {}}, "events": [{"id": 35437940894733, "type": "Change", "value": "29027579134605", "field_name": "assignee_id", "previous_value": null}, {"id": 35437940894861, "type": "Change", "value": "open", "field_name": "status", "previous_value": "new"}, {"id": 35437940894989, "type": "Change", "value": "18758538718221", "field_name": "custom_status_id", "previous_value": "18758522787725"}, {"id": 35437940895117, "type": "Change", "value": "2", "field_name": "31723397869581", "previous_value": null}, {"id": 35437940895245, "type": "Change", "value": "2", "field_name": "31723406578573", "previous_value": null}], "via": {"channel": "web", "source": {"from": {}, "to": {}, "rel": null}}}, {"id": 35437957154445, "ticket_id": 1462310, "created_at": "2025-03-30T21:05:18Z", "author_id": 29027579134605, "metadata": {"system": {"client": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "ip_address": "***************", "location": "Rio de Janeiro, RJ, Brazil", "latitude": -22.9072, "longitude": -43.1883}, "custom": {}}, "events": [{"id": 35437957154573, "type": "Comment", "author_id": 29027579134605, "body": "<PERSON><PERSON> tarde, pessoal.\n\nPodem enviar a via loja POS e a pré-venda?", "html_body": "<div class=\"zd-comment\" dir=\"auto\"><PERSON><PERSON> tarde, pessoal.<br>&nbsp;<br>Podem enviar a via loja POS e a pré-venda?<br></div>", "plain_body": "<PERSON><PERSON> tarde, pessoal.\n&nbsp;\nPodem enviar a via loja POS e a pré-venda?", "public": true, "attachments": [], "audit_id": 35437957154445}, {"id": 35437957154701, "type": "Change", "value": "pending", "field_name": "status", "previous_value": "open"}, {"id": 35437957154829, "type": "Change", "value": "18758564066317", "field_name": "custom_status_id", "previous_value": "18758538718221"}, {"id": 35437957154957, "type": "Change", "value": "6", "field_name": "31723397869581", "previous_value": "2"}, {"id": 35437957155085, "type": "Change", "value": "8", "field_name": "31723406578573", "previous_value": "2"}, {"id": 35437957155213, "type": "Notification", "via": {"channel": "rule", "source": {"from": {"deleted": false, "title": "Notificar o solicitante sobre a atualização de comentários (Status Pendente)", "id": 28776431146509, "revision_id": 5}, "rel": "trigger"}}, "subject": "[Atualização] Ticket {{ticket.id}}", "body": "{{ticket.latest_comment_html}}.", "recipients": [27553558364301]}], "via": {"channel": "web", "source": {"from": {}, "to": {"address": "<EMAIL>", "name": "ef7362"}, "rel": null}}}, {"id": 35501528468237, "ticket_id": 1462310, "created_at": "2025-04-01T21:02:18Z", "author_id": -1, "metadata": {"system": {}, "custom": {}}, "events": [{"id": 35501528468365, "type": "WebhookEvent"}, {"id": 35501528468493, "type": "Change", "value": ["7362_eus03_eusdeque_ce_ef", "anexo_comprovante_pos_via_loja", "atualizador_agente", "cancelamento_cancelamento_venda_em_pix", "csc_fechar_ticket_pendente", "form_default_fields_yes", "lojas_extrafarma", "ptp_otc_cancelamento", "tipo_venda_pix_poslio", "venda_finalizada_sim"], "field_name": "tags", "previous_value": ["7362_eus03_eusdeque_ce_ef", "anexo_comprovante_pos_via_loja", "atualizador_agente", "cancelamento_cancelamento_venda_em_pix", "form_default_fields_yes", "lojas_extrafarma", "ptp_otc_cancelamento", "tipo_venda_pix_poslio", "venda_finalizada_sim"]}], "via": {"channel": "rule", "source": {"to": {}, "from": {"deleted": false, "title": "[CSC] FECHAR ticket PENDENTE 20h Comerciais", "id": 30390036133261}, "rel": "automation"}}}, {"id": 35501500282253, "ticket_id": 1462310, "created_at": "2025-04-01T21:02:19Z", "author_id": 30204482871821, "metadata": {"system": {"client": "Zendesk Webhook", "ip_address": "*************", "location": "United States", "latitude": 37.751, "longitude": -97.822}, "custom": {}}, "events": [{"id": 35501500282381, "type": "Comment", "author_id": 30204482871821, "body": "<PERSON><PERSON><PERSON>,\n Informamos que seu chamado foi fechado por falta de retorno.\n Caso ainda precise seguir com esse atendimento favor seguir com um ticket de acompanhamento ou um novo chamado.", "html_body": "<div class=\"zd-comment\" dir=\"auto\"><PERSON><PERSON><PERSON>, <br> Informamos que seu chamado foi fechado por falta de retorno.<br> <PERSON><PERSON><PERSON> ainda precise seguir com esse atendimento favor seguir com um ticket de acompanhamento ou um novo chamado.</div>", "plain_body": "<PERSON><PERSON><PERSON>, \n Informamos que seu chamado foi fechado por falta de retorno.\n Caso ainda precise seguir com esse atendimento favor seguir com um ticket de acompanhamento ou um novo chamado.", "public": true, "attachments": [], "audit_id": 35501500282253}, {"id": 35501500282509, "type": "Change", "value": "closed", "field_name": "status", "previous_value": "pending"}, {"id": 35501500282637, "type": "Change", "value": "18758520612365", "field_name": "custom_status_id", "previous_value": "18758564066317"}], "via": {"channel": "api", "source": {"from": {}, "to": {}, "rel": null}}}], "meta": {"has_more": false, "after_cursor": "eyJvIjoiY3JlYXRlZF9hdCxpZCIsInYiOiJaRnRVN0djQUFBQUFhWTAxVU5aSklBQUEifQ==", "before_cursor": "eyJvIjoiY3JlYXRlZF9hdCxpZCIsInYiOiJaRitXNkdjQUFBQUFhWTBOWmNRNElBQUEifQ=="}, "links": {"prev": "https://paguemenossac.zendesk.com/api/v2/tickets/1462310/audits.json?page%5Bbefore%5D=eyJvIjoiY3JlYXRlZF9hdCxpZCIsInYiOiJaRitXNkdjQUFBQUFhWTBOWmNRNElBQUEifQ%3D%3D&page%5Bsize%5D=100", "next": "https://paguemenossac.zendesk.com/api/v2/tickets/1462310/audits.json?page%5Bafter%5D=eyJvIjoiY3JlYXRlZF9hdCxpZCIsInYiOiJaRnRVN0djQUFBQUFhWTAxVU5aSklBQUEifQ%3D%3D&page%5Bsize%5D=100"}}