{"firefox": {"default_profile_path": "C:\\Users\\<USER>\\AppData\\Roaming\\Mozilla\\Firefox\\Profiles\\", "profile_production": "xm5avlps.estorno", "profile_homo": "zdkdc64x.estorno", "download_path_eccomerce": "C:\\cancel_cartao_temp_eccomerce", "download_path_poslio": "C:\\cancel_cartao_temp_poslio", "download_path_PDV": "C:\\cancel_cartao_temp_PDV", "download_path_retorno": "C:\\cancel_cartao_temp_retorno", "download_path_vtex_manual": "C:\\cancel_cartao_temp_vtex_manual", "download_path_posV3": "C:\\cancel_cartao_posV3", "download_path_tefV3": "C:\\cancel_cartao_tefV3"}, "botcity": {"key_botcity": "PME_PDDJFSYKUJRJ405QLZ2L", "login_botcity": "pmenos", "server_botcity": "https://paguemenos.botcity.dev/"}, "pagaleve": {"prd": {"label": "paga_leve_prd", "pm": {"username": "username_pm", "password": "password_pm"}, "exf": {"username": "username_exf", "password": "password_exf"}}}, "mercadopago": {"api_estorno_url": "https://api.mercadopago.com/v1/payments/{}/refunds", "api_consulta_url": "https://api.mercadopago.com/v1/payments/{}", "api_token_pos_pgm": "Bearer APP_USR-893061951428055-070212-01e1cd1fce197dd34ab24a09e507e5c2-783710408", "api_token_tef_pgm": "Bearer APP_USR-2397395825378559-102914-3418ba0bf309e84eda6fcdf3fe6c0bb1-794215104", "api_token_pos_ext": "Bearer APP_USR-1114881559197147-102416-f805e6348e4dc7b2ac20a7b19f7a4332-1222247418", "api_token_tef_ext": "Bearer APP_USR-4470422836882084-101410-841208f69f14dc8bd331d73c2f3f1872-1216930490"}, "nupay": {"test_env": {"request_refund_url": "https://sandbox-api.spinpay.com.br/v1/checkouts/payments/{}/refunds", "search_sale_url": "https://sandbox-api.spinpay.com.br/v1/checkouts/payments/{}/status", "search_refund_url": "https://sandbox-api.spinpay.com.br/v1/checkouts/payments/{}/refunds/{}", "pm": {"X-Merchant-Token": "ODUwNjYzMjIxNzBhZTgxNjliNmNhY2QxNmViYzljODk=", "X-Merchant-Key": "c9bf6eff-7e5e-410e-8fc3-1b2d2127cf45"}}, "prd_env": {"request_refund_url": "https://api.spinpay.com.br/v1/checkouts/payments/{}/refunds", "search_sale_url": "https://api.spinpay.com.br/v1/checkouts/payments/{}/status", "search_refund_url": "https://api.spinpay.com.br/v1/checkouts/payments/{}/refunds/{}", "pm": {"X-Merchant-Token": "N2NhZGI4MzA0OTg0ZmViYjY3YzBlNmRlMGRmNmYwNjA=", "X-Merchant-Key": "fc024258-c459-4ad4-8590-ddde9e6c92b6"}, "exf": {"X-Merchant-Token": "ODgzNGExMTI1MzcxZTgwNTZlMDcyMTcyNmQyYjY4YjU=", "X-Merchant-Key": "82e3c789-e679-4d4e-86bc-0d99e92098a9"}}}, "itau": {"api_estorno_url": "https://secure.api.itau/pix_recebimentos/v2/pix/{}/devolucao/{}", "api_consulta_estorno_url": "https://secure.api.itau/pix_recebimentos/v2/pix/{}/devolucao/{}", "api_consulta_url": "https://secure.api.itau/pix_recebimentos/v2/pix/{}", "api_token_url": "https://sts.itau.com.br/api/oauth/token", "client_id_pm": "8d117b95-61c3-4194-996f-8d03fcfb6f10", "client_secret_pm": "54543211-ad74-49e5-84ac-781e758b849e", "client_id_ef": "71a448fe-ae86-4551-890e-e53857837721", "client_secret_ef": "6e6d2466-32de-4c73-85ab-e891483345be"}, "api": {"fluig": {"consumer_key": "PostFluigWCM", "consumer_secret": "PostFluigWCM", "access_token": "876ec86c-d3fd-4611-965f-6ff6d7cb9c2b", "access_token_secret": "84e9e181-8362-495f-b06e-5ad0c4c247109601549d-762d-48e8-8100-aeeac85ccf8d", "url": "https://pmenos.fluig.cloudtotvs.com.br/api/public/ecm/dataset/datasets"}, "sms": {"api_base_url": "https://mc5dpvdc4-brvv2-tc7t2z1g-758.{}", "token": "auth.marketingcloudapis.com/v2/token", "interaction": "rest.marketingcloudapis.com/interaction/v1/events"}, "lio": {"api_base_url": "https://api.velocepdv.com.br", "acessar": "/api/v1/acessar", "get_venda": "/api/v1/pagamentos?dt_inicial={}&dt_final={}&cnpj={}&HeaderAccept=application/json&token={}", "login": "<EMAIL>", "senha": "9HA_zx3u0+=EQ*U"}, "vtex": {"api_base_url": "https://paguemenos{}.vtexcommercestable.com.br/api/oms/pvt/orders/{}", "api_base_url_manual": "https://paguemenos{}.vtexcommercestable.com.br/api/oms/pvt/orders/seq{}", "api_key": "vtexappkey-paguemenos-WZOVDM", "api_token": "TYFWROPAYCKQAKKHEOQFXXXZBEKRXKUCBVNVJYRALYEJXVISPCIKWRDVBGVNEXCNHHPEXPXFWPUZTNCFVSCUCKFJSRTDDEWNVQCPGEVZECNHXECNDPRMVHLXBFLFVUCS"}, "getnet": {"api_base_url_": "https://scows.getnet.com.br:443/requestCancelWS/2.0/RequestCancelService?WSDL", "api_base_url": "https://cancelservice.getnet.com.br/requestCancelWS/2.0/RequestCancelService?WSDL", "username": "PAGUEMENOS_54010", "password": "#p@GU3@MeN#s@2021", "username_": "PAG_MENOS", "password_": "P@gueMenos_+=2021?", "username_ext": "EXTRAFRAMA_4351338", "password_ext": "3St@rn@#$@.V3n5@!22"}, "movimento-bpm": {"api_base_url_": "http://localhost:5942/api/{}", "api_base_url": "http://services.pmenos.com.br/estornocartaoservice/api/{}", "data_corte_busca": "2021-03-29"}, "cielo": {"api_base_url": "https://api2.cielo.com.br", "api_token": "Bearer 835b7f00-5139-40d6-9e33-eb16a267caa1", "resource_consulta": "/refunds-api/v1/refunds/{}/{}?limit=10&offset=1", "resource_refresh_token": "/v2/oauth/access-token", "resource_refresh_token_ext": "/consent/v1/oauth/access-token", "resource_refund": "/refunds-api/v1/refunds", "authorization_pm": "Basic M2Y4MDdmODgtN2FmOS00NmQyLWJjMDYtNjc3ZDVkNjBlNmFkOjljMzBiNzE4LWZhYzUtNGVjOS1iZmNmLWIzNjE3NzY1YzRjMQ==", "authorization_ext": "Basic OGQ1ODk1NTYtY2ZiMi00MzgzLWFkY2ItNjhmY2RhZTU5ZmQzOmQ0MTNmYTYzLTY1ZDAtNGU2My1iZDEwLWIwY2MyMGFkYzRjYw=="}, "zendesk": {"homo": {"subdomain": "paguemenossac1732284657.zendesk.com", "token": "q5gqRzPYBWtxTgzlOzuufis6kJ7qyFkOs6rMNuMw", "user_email": "<EMAIL>", "user_id": "29386022028941"}, "prd": {"subdomain": "paguemenossac.zendesk.com", "token": "JZrE04sk3R7YZdZo42wuezdK7E6kZijTXeFA8Xa8", "user_email": "<EMAIL>", "user_id": "null"}}}, "portal": {"adquirentes": ["cielo", "rede"], "scope": {"url": "http://consultascope.pmenos.com.br/adm/login.xhtml", "user": "90004744", "password": "epm.2020"}}, "bot": {"user": "90004744", "email": "<EMAIL>", "password": "epm.2020", "email_provider": "outlook"}, "email": {"adquirente": {"cielo_envio": "<EMAIL>", "cielo_retorno": "<EMAIL>", "cielo_assunto_retorno": "Cancelamentos", "rede_envio": "<EMAIL>", "diretorio_retorno_cielo": "cielo-retorno"}, "contas_a_receber": ["<EMAIL>", "<EMAIL>", "<EMAIL>", "robert<PERSON><PERSON><PERSON>@pmenos.com.br", "<EMAIL>", "<EMAIL>", "<EMAIL>"], "vtex_manual": {"email": "<EMAIL>", "assunto": "Estorno Manual Requerido"}, "no-reply-email": "<EMAIL>"}, "resource": {"ecommerce": "Estabelecimento/Ecommerce?adquirente={}&filial={}", "pdv": "Estabelecimento/Pdv?adquirente={}&filial={}", "getnet": "Getnet/V3?data=2021-03-29", "cielo": "Getnet/Cielo?data=2021-03-29", "estabelecimento": "Estabelecimento?codEstabelecimeno={}", "terminal": "Estabelecimento/Terminal?codEstabelecimeno={}", "terminaleco": "Estabelecimento/TerminalEco?codEstabelecimeno={}", "buscapornumVtex": "Estorno/buscapornumvtex", "buscaporcoofilaldatapdv": "Estorno/buscaporcoofilaldatapdv", "buscarCnpj": "Estorno/buscarCnpj", "buscaNsuTef": "Estorno/buscarNsuTef", "updaterds": "EstornoFluig/updaterdsligero", "scopeLigero": "Scope/scopeLigero"}, "estrutura": {"database": "\\src\\database\\", "templates": {"cielo": "\\src\\templates\\cielo.xlsx", "rede": "\\src\\templates\\rede.xlsx"}}, "ligero_config": {"base_url": "https://csc.pmenos.com.br/otrs/nph-genericinterface.pl/Webservice/ligero/{method}", "process_dynamic_fields": ["TIPOVENDA", "DATA", "LOJA", "MOTESTORNODEVOLUCAO", "NUMERCOO", "NUMPEDECOMW", "VALORCANC", "VALORTOTCART", "DATAVEND", "PDV", "NUMCUPOM"], "username": "90004744", "password": "epm.2020", "queue_ids": {"CSC_ADMIN1": 135, "CSC_ADMIN2": 136, "G_dir_adm": 131, "Contas_receber": 17}, "state_ids": {"novo": 1, "andamento": 4, "pendente": 13, "pendenteRPA": 13, "pendenteScope": 13, "resolvido": 14, "resolvidopix": 14, "resolvidofinalizado": 14, "resolvidosemnumerovtex": 14, "resolvidonumerofilialinvalido": 14, "resolvidosemnumeropdv": 14, "resolvidosemnumcoo": 14, "resolvidodatamaiorqueatual": 14, "resolvidovendanaolocalizada": 14, "resolvidovendacancelada": 14, "resolvidovendanaoefetuada": 14, "encerrado": 2}, "service_id": 97, "articles": {"andamento": {"CommunicationChannel": "Email", "ContentType": "text/plain; charset=UTF-8", "Body": "Aguardando retorno da adquirente", "Subject": "Aguardando retorno da adquirente"}, "pendente": {"CommunicationChannel": "Email", "ContentType": "text/plain; charset=UTF-8", "Body": "Nao foi possivel Encontrar a venda com os dados Informados, foi aberta uma solicitacao com o numero {} para o time de Contas a Receber.", "Subject": "Pendente com o time de Contas a Receber"}, "pendenteRPA": {"CommunicationChannel": "Email", "ContentType": "text/plain; charset=UTF-8", "Body": "<PERSON><PERSON>, prezados! tudo bem? O ticket foi enviado para adquirente, o Prazo de retorno do atendimento e de ate 24H, por favor nao responder essa mensagem!", "Subject": "Aguardando retorno do Adquirente"}, "pendenteScope": {"CommunicationChannel": "Email", "ContentType": "text/plain; charset=UTF-8", "Body": "<PERSON><PERSON>, prezados! tudo bem? Precisamos aguardar 24H para que a venda possa ser estornada, apos esse peirodo o chamado sera atualizado, por favor nao responder essa mensagem! ", "Subject": "Solicitacao Pendente"}, "resolvidofinalizado": {"CommunicationChannel": "Email", "ContentType": "text/plain; charset=UTF-8", "Body": "Ao analisar o estorno informado, verificamos que a venda ja se encontra Cancelada!", "Subject": "FINALIZADO"}, "resolvidosemnumerovtex": {"CommunicationChannel": "Email", "ContentType": "text/plain; charset=UTF-8", "Body": "Encerrando o ticket Pois o numero do pedido E-commerce e invalido. Solicitamos que verifique se o numero esta correto de acordo com o numero do pedido na VTEX.", "Subject": "FINALIZADO"}, "resolvidosemnumeropdv": {"CommunicationChannel": "Email", "ContentType": "text/plain; charset=UTF-8", "Body": "Encerrando o ticket Pois o numero do PDV e invalido.", "Subject": "FINALIZADO"}, "resolvidonumerofilialinvalido": {"CommunicationChannel": "Email", "ContentType": "text/plain; charset=UTF-8", "Body": "Encerrando o ticket Pois o numero da Filial e invalido.", "Subject": "FINALIZADO"}, "resolvidovendanaolocalizada": {"CommunicationChannel": "Email", "ContentType": "text/plain; charset=UTF-8", "Body": "<PERSON><PERSON>, prezados! tudo bem? Encerrando o ticket Pois a venda nao foi localizada com os dados infomados, caso seja verificado algum dado incorreto abrir outro chamado infomando os dados corretos. ", "Subject": "FINALIZADO"}, "resolvidopix": {"CommunicationChannel": "Email", "ContentType": "text/plain; charset=UTF-8", "Body": "A fila de solicitacao atual realizar apenas estorno de cartoes caso deseje solicitar um estorno de pix abrir novo ticket para a fila Financeiro::Contas a Receber::Cancelamento de Venda Pix.", "Subject": "FINALIZADO"}, "resolvidosemnumcoo": {"CommunicationChannel": "Email", "ContentType": "text/plain; charset=UTF-8", "Body": "Encerrando o ticket Pois o numero do COO e invalido.", "Subject": "FINALIZADO"}, "resolvidovendacancelada": {"CommunicationChannel": "Email", "ContentType": "text/plain; charset=UTF-8", "Body": "Encerrando o ticket Pois a venda consta como CANCELADA.", "Subject": "FINALIZADO"}, "resolvidovendanaoefetuada": {"CommunicationChannel": "Email", "ContentType": "text/plain; charset=UTF-8", "Body": "Encerrando o ticket Pois a venda consta como NAO EFETUADA.", "Subject": "FINALIZADO"}, "resolvidodatamaiorqueatual": {"CommunicationChannel": "Email", "ContentType": "text/plain; charset=UTF-8", "Body": "Encerrando o ticket Pois a Data informada é maior que a data atual.", "Subject": "FINALIZADO"}, "encerrado": {}}}, "api-estorno": {"base_url": "http://services.pmenos.com.br/estornocartaoservice/api/{}"}, "cielo_body_envio": {"transactions": [{"nsu": "", "merchantID": "", "authorizationCode": "", "refundAmount": {"currency": "BRL", "value": 0}, "saleAmount": {"currency": "BRL", "value": 0}, "transactionDate": ""}]}}