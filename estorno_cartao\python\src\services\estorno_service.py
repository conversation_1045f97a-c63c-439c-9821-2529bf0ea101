import requests
from services.fluig_services import FluigServices
from Connections.update_db import UpdateDb
from Connections.search_db import SearchDb
from Connections.insert import InsertMaster
from services.zendesk.zen_services import create_zendesk_ticket
from models.refund_log import *
import Config.vars as vars
from datetime import datetime
from typing import Union, Dict
from dataclasses import asdict
from csv import DictWriter
from services.multi_report_handler import *
from services.logger import create_logger


if __name__ == '__main__': 
    db_refund_logger = create_logger('')
    db_refund_logger.setLevel(10)
else: 
    db_refund_logger = create_logger(__name__, without_handler = True)


class EstornoService():

    def __init__(self, config:dict):
                             
        self.__api_base_url = config['api']['movimento-bpm']['api_base_url']        
        self.__resource_estorno = 'EstornoControllerV3' 
        self.__resource_arquivo = 'Arquivo' 
        self.fluig_service = FluigServices(config)
        self.update_db = UpdateDb()      
        super().__init__()           

    def record_refund_processment(self, dados_estorno: Union[Dict, EstornoLog]) -> None:  

        refund_info = dados_estorno if isinstance(dados_estorno, EstornoLog) else turn_estorno_log_to_dataclasse(dados_estorno)


        if (refund_info.status == vars.STATUS_REFUND_PROCESS_END) and (vars.PIX_GENERAL_NAME in refund_info.bandeira.lower()):
            refund_info.detalhamento = vars.REFUND_PROCESS_END_OBSERVATION_PIX

        elif (vars.STEM_FOR_STATUS_WAIT_ACQUIRE in refund_info.status):
            refund_info.detalhamento = vars.WAIT_OBSERVATION
        
        elif (refund_info.status == vars.STATUS_WAIT_PROOF_OF_REFUND):
            refund_info.detalhamento = vars.PROOF_OF_REFUND_OBSERVATION
        
        elif (refund_info.status == vars.STATUS_PENDENCY_FOR_CSC):
            refund_info.detalhamento = vars.PENDENCY_FOR_CSC_OBSERVATION
        else:
            db_refund_logger.warning(f"Não há um detalhamento para o status: {refund_info.status}")

        record_processment_in_bd(refund_info)

        
        if (vars.STATUS_PENDENCY_FOR_CSC in refund_info.status) and ("V2" not in refund_info.motivo):

            zen_ticket_res = create_zendesk_ticket(dados_estorno, env = "prd")

            if 'error' in zen_ticket_res.data:
                refund_info_dict = asdict(refund_info)
                with open(f"erro_ao_criar_ticket-{datetime.today().strftime('%d_%m_%Y - %H_%M_%S')}.csv", "w") as file:
                    writer = DictWriter(file, fieldnames=list(refund_info_dict.keys()))
                    writer.writeheader()
                    writer.writerow(refund_info_dict)

                msg = f"ERRO na criação do chamado. Id_estorno: {refund_info.idEstorno}"
                log_report(msg)
                print(msg)
                return -1
            
            zen_id = zen_ticket_res.data.get("audit","erro-ticket").get("ticket_id","erro-ticket")
            self.update_db.record_zendesk_ticket_id(refund_info.idEstorno, vars.REQUEST_STAGE ,zen_id)
            return 0
    
    def update_refund_processment(self, dados_estorno: Union[Dict, EstornoLog]) -> None:

        finder = SearchDb()

        if isinstance(dados_estorno, EstornoLog):
            refund_info = dados_estorno

        elif isinstance(dados_estorno, dict) and len(dados_estorno) <= 10:
            refund_info_raw = finder.search_refund_by_cab_approach(
            auth_code = dados_estorno["codigoAutorizacao"],
            id_cab = dados_estorno["nomeArquivo"],
            branch_num = dados_estorno['codigoFilialOrigem'],
            sale_date = dados_estorno["dataVenda"]
            )

            refund_info = estorno_venda_log_orm(refund_info_raw[0][0])
            refund_info.status = dados_estorno["status"]
            refund_info.motivo = dados_estorno["motivo"]

        else:
            refund_info_raw = finder.search_refund_by_cab_approach(
            auth_code = dados_estorno["codigoAutorizacao"],
            id_cab = dados_estorno["nomeArquivo"],
            branch_num = dados_estorno['codigoFilialOrigem'],
            sale_date = dados_estorno["dataVenda"]
            )

            refund_info = estorno_venda_log_orm(refund_info_raw[0][0])
            refund_info.status = dados_estorno["status"]
            refund_info.motivo = dados_estorno["motivo"]

        updater = UpdateDb()
        updater.update_processment(refund_info)

        ZENDESK_KICK_OFF = datetime(day=16, month=9, year=2024)
        if ((vars.STATUS_PENDENCY_FOR_CSC in refund_info.status) 
        and ("V2" not in refund_info.motivo)
        and (datetime.strptime(refund_info.dataDevolucao, "%Y-%m-%d") > ZENDESK_KICK_OFF)
        and refund_info.instancia is None):
            
            zen_ticket_res = create_zendesk_ticket(refund_info, env = "prd")

            if 'error' in zen_ticket_res.data:
                refund_info_dict = asdict(refund_info)
                with open(f"erro_ao_criar_ticket-{datetime.today().strftime('%d_%m_%Y - %H_%M_%S')}.csv", "w") as file:
                    writer = DictWriter(file, fieldnames=list(refund_info_dict.keys()))
                    writer.writeheader()
                    writer.writerow(refund_info_dict)

                msg = f"ERRO na criação do chamado. Id_estorno: {refund_info.idEstorno}"
                log_report(msg)
                print(msg)
                return -1

            zen_id = zen_ticket_res.data.get("audit","erro-ticket").get("ticket_id","erro-ticket")
            self.update_db.record_zendesk_ticket_id(refund_info.idEstorno, vars.RETURN_STAGE, zen_id)
    


    def atualizar_retorno_arquivo(self, id_arquivo:str) -> None:                              
        _url = self.__api_base_url.format(self.__resource_arquivo+'/'+id_arquivo)
        response = requests.put(url=_url)
        if not response.ok:
        
            raise ValueError('Erro na requisicao: status {}'.format(response.text))  

    


def record_processment_in_bd(refund_info: EstornoLog):
    
    refund_mapping_to_record = {
        "ID_ESTORNO": "idEstorno",
        "FILIAL_ORIGEM": "codigoFilialOrigem",
        "CNPJ_FILIAL_ORIGEM": "cnpjFilialOrigem",
        "FILIAL_DESTINO": "codigoFilialDestino",
        "CNPJ_FILIAL_DESTINO": "cnpjFilialDestino",
        "NUMERO_CUPOM": "numeroCupom",
        "NUMERO_COO": "numeroCoo",
        "PDV_DEVOLUCAO": "pdvInformado",
        "NOME_CLIENTE": "nomeCliente",
        "NUMERO_VTEX": "numeroPedidoVTEX",
        "NUMERO_DELIVERY": "numeroPedidoDelivery",
        "DATA_MOVIMENTO": "dataMovimento",
        "VALOR_CUPOM": "valorTotalVenda",
        "VALOR_ESTORNO": "valorEstorno",
        "DATA_DEVOLUCAO": "dataDevolucao",
        "NSU_TEF": "nsutef",
        "NSU_HOST": "nsuhost",
        "BANDEIRA": "bandeira",
        "TELEFONE_CLEINTE": "telefone",
        "EMAIL_CLEINTE": "email",
        "NUMERO_PRE_VENDA": "numeroPreVenda",
        "E_PLANO_ASSINATURA": "planoAssinatura",
        "CANAL_VENDAS": "canalVendas",
        "STATUS": "status",
        "MOTIVO": "motivo",
        "NUMERO_CARTAO_SEMPRE": "cartaoSempre",
        "FLDE_QT_PAR_CAR": "parcelas",
        "FLDE_TP_POS": "tipo",
        "E_PAGAMENTO_UNIFICADO": "flagPagamentoUni",
        "NSU_POS": "nsuPos",
        "ID_DEVTROCA_CAB": "idDevtrocaCab",
        "COO_PAGAMENTO_UNIFICADO": "cooPagUni",
        "DATA_VENDA": "dataVenda",
        "T_ID": "tid",
        "CODIGO_AUTORIZAÇÂO": "codigoAutorizacao",
        "NOME_EMPRESA": "nomeEmpresa",
        "ID_PIX": "id_pix",
        "DATA_VENDA": "dataMovimento",
        "ID_ESTORNO_CAB": "idEstornoCab",
        "PROTOCOLO_GETNET":"protocoloGetnet",
        "PROTOCOLO_CIELO":"protocoloCielo",
        "DETALHAMENTO":"detalhamento",
        "ADQUIRENTE": "adquirente",
        "ID_ESTORNO_ADQUIRENTE": "id_estorno_adquirente"
    }

    values_dict = {
        db_col: getattr(refund_info, attr)
        for db_col, attr in refund_mapping_to_record.items()
        if getattr(refund_info, attr) is not None
    }

    inserter = InsertMaster()

    inserter.insert_refund_processment(values_dict)

    
    


    
    