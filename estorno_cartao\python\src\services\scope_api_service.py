from pathlib import Path
import sys
from datetime import datetime
from datetime import datetime
import time
from typing import Dict, <PERSON>ple
import requests 

def local_execution_with_others_projects_modules(current_file: str, directory_to_go_back: int = 1) -> Path:
    """
    If in a local execution the current module uses resources from another \n
    module that is in the same project i.e.(this module is in the same \n
    directory as the other module) and they aren't in the same level. \n
    So you need to add the common dictory between the modules to the Paths of execution \n
    
    Args:
        `current_file` (str): file path who calls the other modules

    Returns:
        `Path`: common directory Path
    """
    
    src_folder = Path(current_file.split(":")[0].upper() + ":" + current_file.split(":")[1])
    for _ in range(directory_to_go_back): src_folder = src_folder.parent
    if src_folder.__str__() not in sys.path: sys.path.append(str(src_folder))
    if Path.cwd().__str__() not in sys.path: sys.path.append(Path.cwd().__str__())

    return src_folder

src_folder = local_execution_with_others_projects_modules(__file__, 2)
path_elements = src_folder.__str__()


from Config.database import query_all_db, query_all_db_dw
from services.logger import create_logger
import Config.vars  as g_vars

if __name__ == '__main__': 
    scope_logger = create_logger('')
    scope_logger.setLevel(10)
else: 
    scope_logger = create_logger(__name__, without_handler = True)



class SaleNotFound(Exception):

    def __init__(self, message = None) -> None:
        super().__init__(message)

class ScopeApiService():
    
    def __init__(self, config:dict):            
        self.__api_base_url = config['api']['movimento-bpm']['api_base_url']
        self.__resource = 'Scope'
        self.__resource_estrafarma = 'Scope/Extrafarma'
        super().__init__()
    


    def find_sale(self, data_venda:str, filial:int, nsu:str,empresa:str, raw_refund_info: Dict) -> Tuple[Dict, int]:

        try:
            
            dados_scope = self.get_sale(data_venda, filial, raw_refund_info['valorTotalVenda'], nsu, raw_refund_info['valorEstorno'], raw_refund_info)

        except SaleNotFound:
            scope_logger.info("Usando antiga consulta scope via API")
            dados_scope = self.get_venda(data_venda, filial, raw_refund_info['valorTotalVenda'], nsu, raw_refund_info['valorEstorno'],empresa)
        
        except Exception as error:
            print("Erro não mapeado na pesquisa de venda")
            print(f"Detlahes: {error}")
            return -1
        
        return dados_scope


    def get_sale(self, data_venda:str, filial:int, valor_venda:float, nsu:str,valor_estorno:float, raw_refund_info: Dict) ->Tuple[Dict, int]:

        try:

            sale_info = get_sale_info(data_venda, filial, nsu, raw_refund_info)

            if not self.are_sale_and_refund_correct(valor_venda, sale_info["valorTransacao"], valor_estorno):
                    return -8   
            
            dados_scope = self._format_retorno_vda(sale_info)

        except Exception as error:
            print("Erro ao obter dados da venda na VDA")
            raise SaleNotFound()

        return dados_scope
        
    def get_venda(self, data_venda:str, filial:int, valor_venda:float, nsu:str,valor_estorno:float,empresa:str) -> Tuple[Dict, int]:                          
        request_headers = self.__montar_header(data_venda, filial, nsu)
        print('Parametros Scope',request_headers)
       
        if empresa == 'Pague Menos':
            response = requests.get(self.__api_base_url.format(self.__resource), headers=request_headers)
        elif empresa == 'Extrafarma': 
            response = requests.get(self.__api_base_url.format(self.__resource_estrafarma), headers=request_headers)  
        
        response_dict = response.json()
        
        if not response.ok:
            if response.status_code ==  404: return -1
            if response.status_code == 400: return -1
            raise ValueError('Erro na requisicao: status {}'.format(response.status_code)) 
           
        tamanho_response_dict = 1
        if len(response_dict['vendas']) > 1: 
            tamanho_response_dict = len(response_dict['vendas'])
        
        # Time para requisições na api
        time.sleep(3)
        venda_localizada = self.__localizar_venda(response_dict, valor_venda, tamanho_response_dict,valor_estorno) 
        
        if venda_localizada == -9: return -8
        ### Possivel tratamento de lista de objetos dentro da SCOPE ###                       
        
        return self._format_retorno_scope(venda_localizada) if venda_localizada != {} else -1         
    

    def __montar_header(self, data_venda:str, filial:int, nsu:str):
        return {
                "dataVenda":data_venda,
                "numeroFilial":str(filial),
                "numeroNsu": nsu }

    def __localizar_venda(self, vendas:dict, valor_venda:float, quantidade_vendas: int,valor_estorno:float):   
        if quantidade_vendas > 1:
            # valor_transacao = []
            # for i in range(quantidade_vendas):
            #     _vendas = vendas['vendas'][i]
            #     valor_transacao.append(_vendas['valorTransacao'])

            # valor_venda_scope = sum(valor_transacao)
            try:
                _vendas = vendas['vendas'][0]
            # print(valor_venda)
            # print(valor_venda_scope)

            # if valor_venda == valor_venda_scope:
            #     return _vendas
                return _vendas
            except:
                return {}        

        else:
            
            print("####Venda####")
            print(valor_venda)
            print("####Estorno####")
            print(valor_estorno)
            print("####scope####")
            _vendas = vendas['vendas']
            print(_vendas[0]['valorTransacao'])
            
            _vendas = vendas['vendas']
            
            if (valor_venda == _vendas[0]['valorTransacao'] or valor_estorno == _vendas[0]['valorTransacao']):
                return _vendas[0]    
            
            return -9

    def __validate(self, dados:dict) -> int:
        
        if dados['situacaoVenda'].lower() == 'cancelamento': return -7                     
        if dados['situacaoVenda'].lower() == 'cancelado': return -2
        if dados['situacaoVenda'].lower() == 'desfeito': return -5
        if dados['situacaoVenda'].lower() == 'não efetuado': return -6
        if dados['situacaoVenda'].lower() == 'pendente': return 1
        if dados['situacaoVenda'].lower() != 'ok': return -4
        if dados['adquirente'].lower() == 'getnetlac': return 1
        return 1


    def _format_retorno_vda(self, dados:dict) -> dict:
        
        venda_scope = {}

        # Verificando se venda existe
        if dados == {}: return {}
        
        # Validando venda
        _valide = self.__validate(dados)        
        if _valide != 1: return _valide            

        #venda_scope['data_venda'] = self.__convert_date(dados['dataVenda'])
        venda_scope['data_venda'] = dados['dataVenda']
        venda_scope['data_deposito'] = venda_scope['data_venda']
        #_lote = datetime.strptime(venda_scope['data_venda'], '%d/%m/%Y').strftime('%y%m%d')
        _lote = venda_scope['data_venda'].strftime('%y%m%d')
        venda_scope['lote'] = _lote
        venda_scope['adquirente'] = self.__verific_adquirente(dados)
        venda_scope['valor_compra'] = dados['valorTransacao']
        venda_scope['numero_cartao'] = dados['cartaoXID'].replace('-', '******')
        venda_scope['nsu'] = dados['nsuTef']
        # venda_scope['nsu_host'] = dados['nsuHost']
        try: 
            venda_scope['nsu_host'] = dados['nsuHost'] 
        except: 
            venda_scope['nsu_host'] = False
            
        venda_scope['codigo_autorizacao'] = dados['codigoAutorizacao'] 
        venda_scope['status_venda'] = dados['situacaoVenda']
        venda_scope['seq_controle'] = 'XXXXXXXXX'
        venda_scope['quantidade_parcelas'] = dados['quantidadeParcelas']

        return venda_scope

    def _format_retorno_scope(self, dados:dict) -> dict:
        
        venda_scope = {}

        # Verificando se venda existe
        if dados == {}: return {}
        
        # Validando venda
        _valide = self.__validate(dados)        
        if _valide != 1: return _valide            

        venda_scope['data_venda'] = self.__convert_date(dados['dataVenda'])
        venda_scope['data_deposito'] = venda_scope['data_venda']
        _lote = datetime.strptime(venda_scope['data_venda'], '%d/%m/%Y').strftime('%y%m%d')
        venda_scope['lote'] = _lote
        venda_scope['adquirente'] = self.__verific_adquirente(dados)
        venda_scope['bandeira'] = dados['nomeBandeira'].lower()
        venda_scope['valor_compra'] = dados['valorTransacao']
        venda_scope['numero_cartao'] = dados['cartaoXID'].replace('-', '******')
        venda_scope['nsu'] = dados['nsuTef']
        # venda_scope['nsu_host'] = dados['nsuHost']
        try: 
            venda_scope['nsu_host'] = dados['nsuHost'] 
        except: 
            venda_scope['nsu_host'] = False
            
        venda_scope['codigo_autorizacao'] = dados['codigoAutorizacao'] 
        venda_scope['status_venda'] = dados['situacaoVenda']
        venda_scope['seq_controle'] = 'XXXXXXXXX'
        venda_scope['quantidade_parcelas'] = dados['quantidadeParcelas']

        return venda_scope

    def __convert_date(self, date:str) -> str:
        _dt = datetime.strptime(date, '%Y-%m-%dT%H:%M:%S')
        return _dt.strftime('%d/%m/%Y')

    def __verific_adquirente(self, dados: dict):
        if dados['adquirente'].lower() == 'getnetlac':
            adquirente = 'getnet'
            return adquirente
        elif dados['adquirente'].lower() == 'cielo':
            adquirente = 'cielo'
            return adquirente
        elif dados['adquirente'].lower() == g_vars.CREDSHOP_ACQUIRE_NAME.lower():
            adquirente = g_vars.CREDSHOP_ACQUIRE_NAME.lower()
            return adquirente
        else: 
            raise SaleNotFound("Adquirente não mapeado")

    
    def are_sale_and_refund_correct(self, valor_venda, sale_total_value, valor_estorno):

        if (valor_venda == sale_total_value or valor_estorno == sale_total_value):

            if not valor_venda == sale_total_value: print("Valor do ticket não bate com valor da venda")
                
            return True

        else:

            return False


def get_main_sale_info_cosmos_fl(data_venda:str, filial:int, nsu:str) -> Dict: 

    find_sale = """SELECT 
                    vda_cab.MVVC_VL_TOT_CPM AS valorTransacao,
                    vda.FLVD_DT_MOV as dataVenda, 
                    vda.FLVD_QT_PAR_CAR as quantidadeParcelas,
                    vda.FLVD_CD_AUT_TEF as codigoAutorizacao,
                    vda.NSU_AUTORIZADORA_TEF as nsuHost,
                    rede_info.nome_rede  as adquirente

                    FROM CosmosFL.dbo.flz_vda vda (nolock)
                    left join cosmosdw.scopehist.dbo.rede rede_info (nolock) 
                        ON (CONVERT(VARCHAR, vda.REDE_SCOPE) = rede_info.cod_rede)
                    inner join CosmosFL..mv_vda_cab vda_cab (nolock)
                        ON (
                    vda.FLVD_CD_FILIAL_MOV = vda_cab.MVVC_CD_FILIAL_MOV  AND
                    vda.FLVD_DT_MOV = vda_cab.MVVC_DT_MOV AND 
                    vda.FLVD_NR_ECF_MOV = vda_cab.MVVC_NR_ECF_MOV AND 
                    vda.FLVD_CT_VDA = vda_cab.MVVC_CT_VDA)

                    WHERE 
                    vda.FLVD_CD_FILIAL_MOV = {branch}
                    and vda.FLVD_DT_MOV = '{sale_date}'
                    and vda.FLVD_NR_NSU_TEF = {nsu_tef}
                    """

    find_sale = find_sale.format(branch = filial,
                                sale_date = data_venda,
                                nsu_tef = nsu)

    sale_info = query_all_db(find_sale)


    if len(sale_info) < 1:
        scope_logger.warning("Não há informação sobre essa venda na VDA da Cosmos_fl")
        raise SaleNotFound()

    return sale_info[0]



def get_main_sale_info_cosmosmov_v14b(data_venda:str, filial:int, nsu:str) -> Dict: 

    find_sale = """ SELECT 
                    vda_cab.MVVC_VL_TOT_CPM AS valorTransacao,
                    vda.FLVD_DT_MOV as dataVenda, 
                    vda.FLVD_QT_PAR_CAR as quantidadeParcelas,
                    vda.FLVD_CD_AUT_TEF as codigoAutorizacao,
                    vda.NSU_AUTORIZADORA_TEF as nsuHost,            
                    rede_info.nome_rede  as adquirente

                    FROM cosmosmov_v14b.dbo.flz_vda vda (nolock)
                    left join cosmosdw.scopehist.dbo.rede rede_info (nolock) 
                        ON (CONVERT(VARCHAR, vda.REDE_SCOPE) = rede_info.cod_rede)
                    inner join cosmosmov_v14b..mv_vda_cab vda_cab (nolock)
                        ON (
                    vda.FLVD_CD_FILIAL_MOV = vda_cab.MVVC_CD_FILIAL_MOV  AND
                    vda.FLVD_DT_MOV = vda_cab.MVVC_DT_MOV AND 
                    vda.FLVD_NR_ECF_MOV = vda_cab.MVVC_NR_ECF_MOV AND 
                    vda.FLVD_CT_VDA = vda_cab.MVVC_CT_VDA)

                    WHERE 
                    vda.FLVD_CD_FILIAL_MOV = {branch}
                    and vda.FLVD_DT_MOV = '{sale_date}'
                    and vda.FLVD_NR_NSU_TEF = {nsu_tef}
                    """

    find_sale = find_sale.format(branch = filial,
                                sale_date = data_venda,
                                nsu_tef = nsu)

    sale_info = query_all_db(find_sale)


    if len(sale_info) < 1:
        scope_logger.warning("Não há informação sobre essa venda na VDA na Comsosmov_v14b")
        raise SaleNotFound()

    return sale_info[0]




def get_aux_sale_info(data_venda:str, filial:int, nsu:str) -> Dict: 

    find_sale = """SELECT  
                    isnull(MSG.COD_AUTORIZACAO,'') AS codigoAutorizacao,
                    ban.nome_bandeira as nomeBandeira,
                    numero_conta as cartaoXID,
                    NSU as nsuTef,
                    nsu_host as nsuHost,
                    CASE
                    WHEN MSG.SITUACAO_MENSAGEM in('D','d') then 'Desfeito'
                    WHEN MSG.SITUACAO_MENSAGEM in('K','k') then 'Cancelamento Pendente'
                    WHEN MSG.SITUACAO_MENSAGEM = '1' then 'Em Andamento'
                    WHEN MSG.SITUACAO_MENSAGEM = '2' then 'Pendente'
                    WHEN MSG.SITUACAO_MENSAGEM = 'M' then 'Manual'
                    WHEN MSG.SITUACAO_MENSAGEM = 'O' then 'OK'-- Scope'OK'
                    WHEN MSG.SITUACAO_MENSAGEM = 'N' then 'Não Efetuado'
                    WHEN MSG.SITUACAO_MENSAGEM = 'C' then 'Cancelado'
                    WHEN MSG.SITUACAO_MENSAGEM = '4' then 'Cancelamento Pendente'
                    WHEN MSG.SITUACAO_MENSAGEM = 'A' then 'Pré-autorização capturada'
                    END	situacaoVenda,
                    cod_rede

                    FROM cosmosdw.scopehist.dbo.mensagem MSG (NOLOCK) 
                    LEFT join cosmosdw.scopehist.dbo.bandeira  ban (NOLOCK) ON MSG.cod_bandeira = ban.cod_bandeira
                    WHERE
                    convert(int, MSG.cod_filial) = convert(int, '{branch}')
                    AND RIGHT(isnull(MSG.NSU,'0'), 6) = convert(int, '{nsu_tef}')
                    AND convert(date, MSG.dthr_server) = CONVERT(DATE, '{sale_date}')
                    """

    find_sale = find_sale.format(branch=filial,
                                sale_date = data_venda,
                                nsu_tef = nsu)

    sale_info = query_all_db_dw(find_sale)


    if len(sale_info) < 1:
        print("Não há informação auxiliares sobre essa venda")
        return None

    if len(sale_info) > 1:
        print("Mais de um registro de venda foi encontrado ")


    return sale_info[0]


def verify_acquirer_by_code(scope_data: Dict) -> str:

        if str(g_vars.GETNET_ACQUIRE_SCOPE_CODE) in scope_data['cod_rede']:
            adquirente = 'getnet'
            return adquirente
        elif str(g_vars.CIELO_ACQUIRE_SCOPE_CODE) in scope_data['cod_rede']:
            adquirente = 'cielo'
            return adquirente
        elif str(g_vars.CREDSHOP_ACQUIRE_SCOPE_CODE).zfill(3) in scope_data['cod_rede']:
            return g_vars.CREDSHOP_ACQUIRE_NAME.lower()
        else: 
            return None
        
def get_sale_info(data_venda:str, filial:int, nsu:str, raw_refund_info: Dict) -> Dict: 

    try:
        main_sale_info = get_main_sale_info_cosmos_fl(data_venda, filial, nsu)
    except SaleNotFound:
        scope_logger.info("Verificando venda na Comsosmov_v14b")

        main_sale_info = get_main_sale_info_cosmosmov_v14b(data_venda, filial, nsu)

    aux_sale_info = get_aux_sale_info(data_venda, filial, nsu)



    sale_info = {
        "valorTransacao": float(main_sale_info["valorTransacao"]),
        "dataVenda": main_sale_info["dataVenda"],
        "quantidadeParcelas": main_sale_info["quantidadeParcelas"],
        "adquirente": main_sale_info["adquirente"],
    }


    if main_sale_info["adquirente"] is None:
        sale_info["adquirente"] = verify_acquirer_by_code(aux_sale_info)
    
    if sale_info["adquirente"] is None and raw_refund_info['bandeira'] == g_vars.CREDSHOP_ACQUIRE_NAME:
        sale_info["adquirente"] = g_vars.CREDSHOP_ACQUIRE_NAME.lower()


    if main_sale_info["codigoAutorizacao"] is not None or main_sale_info["codigoAutorizacao"] != '':
        sale_info["codigoAutorizacao"] = main_sale_info["codigoAutorizacao"]

    elif aux_sale_info is not None:
        sale_info["codigoAutorizacao"] = aux_sale_info["codigoAutorizacao"]

    else:
        raise SaleNotFound("Não foi possível obter o código de autorização")
    
    
    sale_info["nsuTef"] = nsu

    if aux_sale_info is not None:
        sale_info["cartaoXID"] = aux_sale_info["cartaoXID"] # auxiliar
        sale_info["nsuHost"] = aux_sale_info["nsuHost"]
        sale_info["situacaoVenda"] = aux_sale_info["situacaoVenda"] # auxiliar
    else:
        sale_info["cartaoXID"] = 'XXXXXXXX' # auxiliar
        sale_info["nsuHost"] = main_sale_info["nsuHost"]
        sale_info["situacaoVenda"] = 'ok' # auxiliar

    return sale_info





if __name__ == "__main__":


    x = get_sale_info("2024-09-04", 672, 102330)


    print("fim")
