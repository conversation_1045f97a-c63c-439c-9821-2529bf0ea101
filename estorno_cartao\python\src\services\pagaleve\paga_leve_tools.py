
from requests.models import Response
from pathlib import Path
from typing import Dict, Union
import sys
from json import JSONDecodeError

def local_execution_with_others_projects_modules(current_file: str, directory_to_go_back: int = 1) -> Path:
    """
    If in a local execution the current module uses resources from another \n
    module that is in the same project i.e.(this module is in the same \n
    directory as the other module) and they aren't in the same level. \n
    So you need to add the common dictory between the modules to the Paths of execution \n
    
    Args:
        `current_file` (str): file path who calls the other modules

    Returns:
        `Path`: common directory Path
    """
    
    src_folder = Path(current_file.split(":")[0].upper() + ":" + current_file.split(":")[1])
    for _ in range(directory_to_go_back): src_folder = src_folder.parent
    if src_folder.__str__() not in sys.path: sys.path.append(str(src_folder))
    if Path.cwd().__str__() not in sys.path: sys.path.append(Path.cwd().__str__())

    return src_folder

if __name__ == "__main__":
    src_folder = local_execution_with_others_projects_modules(__file__, 3)

from services.logger import create_logger
from utils import ResultHandle
import services.pagaleve.paga_leve_vars as pgl_vars

if __name__ == '__main__': 
    paglev_tools_logger = create_logger('')
    paglev_tools_logger.setLevel(10)
else: 
    paglev_tools_logger = create_logger(__name__, without_handler = True)



def validate_request(api_res: Response) -> ResultHandle:
    """Criar objeto de resultado para requisição

    Args:
        api_res (Response): resultado da API

    Returns:
        ResultHandle: Objeto de resultado
    """

    try:
        body_response: Dict = api_res.json()
    except Exception as error:
        paglev_tools_logger.error("Erro ao transformar a respota em dict")
        paglev_tools_logger.error(f"Detalhes dos erros: {error}")
        body_response: str = api_res.text


    if api_res.ok: return ResultHandle.Ok(body_response)

    msg  = get_error_message(body_response)

    return ResultHandle.Fail(msg, body_response)
    
    
    


def shrink_message(msg: str, max_lenght_of_chars: int = 254) -> str:
    """Cortar a messagem de modo a não dar erro de overflow no banco

    Args:
        msg (str): String da menssagem 
        max_lenght_of_chars (int, optional): Tamanho máximo da string

    Returns:
        str: Mensagem cortada
    """

    if msg is not None and len(msg) > max_lenght_of_chars:
        msg = msg[:254]
        return msg
    else:
        return msg



def get_error_message(body_response: Union[Dict, str]):

    msg = None
    if isinstance(body_response, dict) and "exception" in body_response:

        if body_response["exception"] in pgl_vars.type_of_standard_erros_and_translated_msg:
            msg = pgl_vars.type_of_standard_erros_and_translated_msg[body_response["exception"]]
        else:
            msg = pgl_vars.NOT_MAPPED_ERROR + body_response["exception"]

    elif isinstance(body_response, dict) and "message" in body_response:

        acquire_msg = body_response["message"][0]
        if acquire_msg in pgl_vars.type_of_standard_erros_and_translated_msg:
            msg = pgl_vars.type_of_standard_erros_and_translated_msg[acquire_msg]
        else:
            msg = pgl_vars.NOT_MAPPED_ERROR + body_response["exception"]
    
    elif isinstance(body_response, str):

        msg = pgl_vars.WITHOUT_JSON_ERROR 

    if msg is not None:
        return shrink_message(msg)
    else:
        return pgl_vars.NOT_MAPPED_ERROR + "Não está nem no EXCEPTION nem na MESSAGE"


     
    
    
        


